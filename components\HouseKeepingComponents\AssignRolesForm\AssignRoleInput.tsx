import React from 'react';
import {
  Input,
  StyleService,
  Text,
  useStyleSheet,
} from '@ui-kitten/components';
import Stack from '~/components/HouseKeepingComponents/ui/Stack';

const AssignRoleInput = ({
  label,
  value,
  placeholder,
  onChangeText,
}: {
  label: string;
  value: string;
  placeholder?: string;
  onChangeText: (text: string) => void;
}) => {
  const styles = useStyleSheet(themedStyle);

  return (
    <Stack>
      <Text category="c1" appearance="hint">
        {label}
      </Text>
      <Input
        value={value}
        style={styles.input}
        onChangeText={onChangeText}
        placeholder={placeholder}
      />
    </Stack>
  );
};

export default AssignRoleInput;

const themedStyle = StyleService.create({
  input: {
    marginVertical: 12,
  },
});

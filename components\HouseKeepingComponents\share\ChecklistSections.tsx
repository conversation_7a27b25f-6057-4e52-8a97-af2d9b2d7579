import React from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  HKChecklist,
  HKTask,
  HKExtendedJob,
  HKUser,
  HKJobSection,
} from '~types';
import ChecklistSection from '~components/HouseKeepingComponents/share/ChecklistSection';
import Stack from '~components/HouseKeepingComponents/ui/Stack';

type Props = {
  sections: HKSection[];
  checklist: HKChecklist;
  tasks: HKTask[];
  job?: HKExtendedJob | null;
  user?: HKUser | null;
  onCompleteSection?: (
    jobSection: Omit<HKJobSection, 'completed_at'>,
  ) => Promise<void>;
  activeSection?: HKSection | null;
  onlyAssigning?: boolean;
};

const ChecklistSections = ({
  job,
  user,
  sections,
  checklist,
  tasks,
  onCompleteSection,
  activeSection,
  onlyAssigning,
}: Props) => (
  <Stack>
    {sections.map(section => (
      <ChecklistSection
        key={section.id}
        job={job}
        user={user}
        section={section}
        checklist={checklist}
        tasks={tasks.filter(task => task.section_id === section.id)}
        disabled={
          onlyAssigning ||
          user?.performing !== checklist ||
          section.id !== activeSection?.id
        }
        onComplete={onCompleteSection}
      />
    ))}
  </Stack>
);

export default ChecklistSections;

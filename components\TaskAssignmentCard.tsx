import React, { ReactElement } from 'react';
import { Card } from '@ui-kitten/components';
import { StyleSheet, View } from 'react-native';
import { differenceInDays } from 'date-fns';
import { useQuery } from 'react-query';
import { TaskAssignment } from '~types';
import { recurringIntervals } from '~dummyData';
import useUser from '~api/useUser';
import { priorityIsUrgentOrAnytime } from '~helpers';
import Spinner from './Spinner';
import useTimezone from '~hooks/useTimezone';
import TaskCardTextContent from './TaskCardComponents/TaskCardTextContent';
import TaskCardHeader from './TaskCardComponents/TaskCardHeader';

type Props = {
  children?: ReactElement;
  onPress?: () => void;
  showDetails?: boolean;
  taskAssignment: TaskAssignment;
};

const TaskAssignmentCard = ({
  children,
  onPress = () => {},
  showDetails,
  taskAssignment: {
    repetitionNumber,
    finishedAt,
    createdAt: acceptedOn,
    expiredAt,
    task: {
      id: taskId,
      media,
      thumbnail,
      description,
      property,
      preferredDate,
      requiredSkills,
      priority,
      creatorUserId,
      recurring,
      createdAt,
      deletedAt,
    },
    user: { id: acceptedByUserId },
  },
}: Props): ReactElement => {
  const { zonedTimeToUtc } = useTimezone();
  const { getUser, getAccountMembers } = useUser();
  const { data: user } = useQuery('user', () => getUser());
  const accountId = user?.accounts[0].id;
  const { isLoading, data: accountMembers = [] } = useQuery(
    ['accountMembers', accountId],
    () => getAccountMembers(accountId, true),
    { enabled: !!accountId },
  );

  const overdueInDays = () => {
    const diffInDays = differenceInDays(
      zonedTimeToUtc(new Date()),
      new Date(preferredDate),
    );

    if (
      diffInDays > 0 &&
      !priorityIsUrgentOrAnytime(priority) &&
      finishedAt === null
    ) {
      return diffInDays;
    }
    return 0;
  };

  const overdue = overdueInDays();

  const recurringId = Number(recurring);

  const creatorUser = accountMembers.find(
    ({ id: userId }) => userId === creatorUserId,
  );

  const acceptedByUser = accountMembers.find(
    ({ id: userId }) => userId === acceptedByUserId,
  );

  if (isLoading || !creatorUser) {
    return <Spinner />;
  }

  const recurringIntervalName = recurringIntervals.find(
    item => item.id === recurringId,
  )?.name;

  if (!recurringIntervalName) {
    return <Spinner />;
  }

  return (
    <View style={styles.container}>
      <Card
        header={
          <TaskCardHeader
            id={taskId}
            finishedAt={finishedAt}
            overdue={overdue}
            priority={priority}
            expired={Boolean(expiredAt)}
            media={media}
            showDetails={showDetails}
            thumbnail={thumbnail}
            acceptedOn={acceptedOn}
            repetitionNumber={repetitionNumber}
            deletedAt={deletedAt}
          />
        }
        onPress={onPress}
      >
        <TaskCardTextContent
          description={description}
          priority={priority}
          isAssignment
          preferredDate={preferredDate}
          property={property}
          recurring={recurring}
          requiredSkills={requiredSkills}
          createdAt={createdAt}
          creatorUser={creatorUser}
          overdue={overdue}
          acceptedByUser={acceptedByUser}
          acceptedOn={acceptedOn}
          finishedAt={finishedAt}
          showDetails={showDetails}
        />
        {children}
      </Card>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: 16,
    marginBottom: 16,
  },
});

export default TaskAssignmentCard;

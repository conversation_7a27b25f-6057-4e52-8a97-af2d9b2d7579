import React from 'react';
import { Text } from '@ui-kitten/components';
import { format } from 'date-fns';

type Props = {
  daysLate: number;
  nextRecurringPreferredDate: string;
  diffInDaysToNextRecurringPreferredDate: number;
};

const RecurringTaskOverdueWarning = ({
  daysLate,
  nextRecurringPreferredDate,
  diffInDaysToNextRecurringPreferredDate,
}: Props) => {
  const formattedNextRecurringPreferredDate = format(
    new Date(nextRecurringPreferredDate),
    'MM/dd',
  );

  return (
    <Text>
      You were {daysLate} days late completing this job. This job will be
      automatically scheduled {diffInDaysToNextRecurringPreferredDate} days from
      today, for {formattedNextRecurringPreferredDate}. If that date does not
      seem to be correct, please contact your supervisor to have the next
      occurrence of this job manually reset.
    </Text>
  );
};

export default RecurringTaskOverdueWarning;

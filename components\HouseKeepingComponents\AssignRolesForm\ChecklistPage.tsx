import React, { useState } from 'react';
import {
  Text,
  StyleService,
  useStyleSheet,
  Button,
} from '@ui-kitten/components';
import { ScrollView, View } from 'react-native';
import {
  HKExtendedJob,
  HKUser,
  HKSection,
  HKJobSection,
  HKTask,
  HKChecklist,
  HKJob,
} from '~types';
import {
  getActiveSection,
  getAllRemainingSections,
  getChecklistsToShare,
} from '~hk-helpers';
import AssignRolesForm from '~components/HouseKeepingComponents/AssignRolesForm/AssignRolesForm';
import LinkForOtherPersonModal from '~components/HouseKeepingComponents/Job/LinkForOtherPersonModal';
import DatesAndPropertyInfo from '~components/HouseKeepingComponents/Job/DatesAndPropertyInfo';
import ShareAndFinishButtons from '~components/HouseKeepingComponents/Job/ShareAndFinishButtons';
import HeaderButtons from '~components/HouseKeepingComponents/Job/HeaderButtons';
import AssignRolesModal from '~components/HouseKeepingComponents/Job/AssignRolesModal';
import AccessInfoModal from '~components/HouseKeepingComponents/Job/AccessInfoModal';
import ChecklistTabs from '~components/HouseKeepingComponents/JobTab/ChecklistTabs';

type Props = {
  job: HKExtendedJob | null;
  sections: HKSection[];
  onStartChecklist: () => Promise<void>;
  onCompleteSection: (
    jobSection: Omit<HKJobSection, 'completed_at'>,
  ) => Promise<void>;
  tasks: HKTask[];
  isLoading: boolean;
  updateJob: () => Promise<void>;
  user: HKUser | null;
  canEditRoles: boolean;
  onClickPDFReport: (() => void) | null;
  onSaveAssignRolesForm?: (job: HKJob) => Promise<void>;
  onClickFinish?: () => void;
  pdfReportPath?: string;
  onlyAssigning?: boolean;
};

const ChecklistPage = ({
  job,
  sections,
  onStartChecklist,
  onCompleteSection,
  tasks,
  isLoading,
  updateJob,
  user,
  canEditRoles,
  onSaveAssignRolesForm,
  onClickPDFReport,
  onClickFinish,
  pdfReportPath,
  onlyAssigning = false,
}: Props) => {
  const styles = useStyleSheet(themedStyles);

  const [geoLocationIsEnabled, setGeoLocationIsEnabled] =
    useState<boolean>(true);
  const [checklistsToShare, setChecklistsToShare] = useState<
    HKChecklist[] | null
  >(null);
  const [accessInfoModalOpened, setAccessInfoModalOpened] = useState(false);
  const [assignRolesModalOpened, setAssignRolesModalOpened] = useState(false);
  const [selectedTab, setSelectedTab] = useState<HKChecklist>('person1');

  const performing = user?.performing ?? null;
  const isLeader = !job || user?.role === 'leader';
  const jobPerformType = job?.performType;
  const remainingSections = getAllRemainingSections(performing, sections, job);
  const activeSection = getActiveSection(performing, sections, job);

  if (!geoLocationIsEnabled) {
    return (
      // <GeoLocationAlert
      //   enabled={geoLocationIsEnabled}
      //   onChange={setGeoLocationIsEnabled}
      // />
      <View style={styles.loadingContainer}>
        <Text>GeoLocation is not enabled</Text>
        <Button onPress={() => setGeoLocationIsEnabled(true)}>
          Enable GeoLocation
        </Button>
      </View>
    );
  }

  if (isLoading) {
    return (
      // <LoadingOverlay visible zIndex={1000} loaderProps={{ type: 'bars' }} />
      <View style={styles.loadingContainer}>
        <Text>Loading...</Text>
      </View>
    );
  }

  const handleClickRoles = canEditRoles
    ? () => setAssignRolesModalOpened(true)
    : null;
  const handleClickPropertyAccess = isLeader
    ? () => setAccessInfoModalOpened(true)
    : null;
  const handleCloseLinkForOtherPersonModal = () => setChecklistsToShare(null);
  const handleCloseAccessInfoModal = () => setAccessInfoModalOpened(false);
  const handleUpdateJob = async () => updateJob();
  const handleSaveAssignRolesForm = async ({
    jobId,
    leaderPerforms,
    leaderName,
    helperName,
    performType,
  }: HKJob) => {
    if (!onSaveAssignRolesForm) {
      return;
    }

    await onSaveAssignRolesForm({
      jobId,
      leaderPerforms,
      leaderName,
      helperName,
      performType,
    });
    setAssignRolesModalOpened(false);
    setSelectedTab(leaderPerforms ?? 'person1');
    if (performType === 'assigned' || leaderPerforms !== 'all') {
      setChecklistsToShare(getChecklistsToShare(performType, leaderPerforms));
    }
  };

  if (!sections || !tasks) {
    return null;
  }

  if (!job) {
    return (
      <ScrollView style={[styles.container, styles.assignRoleFormContainer]}>
        <Text category="p1">
          First you will need to select if you are performing the checklist by
          yourself or with a team member or assign this job completely to other
          people.
        </Text>
        <AssignRolesForm
          onSave={handleSaveAssignRolesForm}
          sections={sections}
          tasks={tasks}
        />
      </ScrollView>
    );
  }
  return (
    <>
      <ScrollView contentContainerStyle={styles.container}>
        <DatesAndPropertyInfo job={job} />
        {(isLeader || jobPerformType === 'assigned') && (
          <HeaderButtons
            onClickPropertyAccess={handleClickPropertyAccess}
            onClickRoles={handleClickRoles}
            onClickReport={onClickPDFReport}
          />
        )}
        <ChecklistTabs
          job={job}
          user={user}
          onStartChecklist={onStartChecklist}
          onCompleteSection={onCompleteSection}
          tasks={tasks}
          performing={performing}
          updateJob={handleUpdateJob}
          isLeader={isLeader}
          selectedTab={selectedTab}
          setSelectedTab={setSelectedTab}
          sections={sections}
          activeSection={activeSection}
          onlyAssigning={onlyAssigning}
        />
        <LinkForOtherPersonModal
          job={job}
          checklists={checklistsToShare}
          opened={!!checklistsToShare?.length}
          onClose={handleCloseLinkForOtherPersonModal}
        />
        <AccessInfoModal
          job={job}
          opened={accessInfoModalOpened}
          onClose={handleCloseAccessInfoModal}
        />
        {canEditRoles && (
          <AssignRolesModal
            values={job}
            opened={assignRolesModalOpened}
            onClose={() => setAssignRolesModalOpened(false)}
            onSave={handleSaveAssignRolesForm}
          />
        )}
      </ScrollView>
      {isLeader && onClickFinish && pdfReportPath && (
        <ShareAndFinishButtons
          style={styles.finishButtonContainer}
          onClickFinish={onClickFinish}
          disabled={remainingSections.length > 0}
          pdfReportPath={pdfReportPath}
        />
      )}
    </>
  );
};

export default ChecklistPage;

const themedStyles = StyleService.create({
  assignRoleFormContainer: {
    paddingHorizontal: 12,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  container: {
    gap: 12,
    padding: 12,
    backgroundColor: 'white',
  },
  finishButtonContainer: {
    paddingHorizontal: 12,
  },
});

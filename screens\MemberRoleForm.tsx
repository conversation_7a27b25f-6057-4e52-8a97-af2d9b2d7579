import React, { useEffect, useLayoutEffect, useState } from 'react';
import {
  Button,
  Input,
  StyleService,
  useStyleSheet,
  CheckBox,
  RadioGroup,
  Radio,
} from '@ui-kitten/components';
import { useMutation, useQuery, useQueryClient } from 'react-query';
import * as Yup from 'yup';
import { View, Text } from 'react-native';
import { StackScreenProps } from '@react-navigation/stack';
import { useToast } from 'react-native-toast-notifications';
import { ProfileSetting } from '~components/ProfileSetting';
import useUser from '~api/useUser';
import roles from '~constants/Roles';
import SpinnerButton from '~components/SpinnerButton';
import Confirm from '~components/Confirm';
import CheckboxesInput from '~components/CheckboxesInput';
import Spinner from '~components/Spinner';
import MemberRoleFormContainer from '~components/MemberRoleFormContainer';
import SwitchInput from '~components/SwitchInput';
import useSkills from '~queries/useSkills';
import RadioAndNumberInput from '~components/RadioAndNumberInput';
import { MoreParamList } from '~types';
import useProperties from '~queries/useProperties';

const schema = Yup.object().shape({
  email: Yup.string().email().required('Required'),
});

enum PropertyAccessType {
  All = 0,
  Specific = 1,
}

type Props = StackScreenProps<MoreParamList, 'MemberScreen'>;

export default ({ navigation, route }: Props): React.ReactElement => {
  const toast = useToast();
  const styles = useStyleSheet(themedStyle);

  enum OperationTypes {
    Invitation = 'invitation',
    Member = 'member',
  }

  const [showConfirm, setShowConfirm] = useState(false);
  const queryClient = useQueryClient();
  const {
    postInvitation,
    getAccountMember,
    editAccountMember,
    deleteAccountMember,
    getInvitation,
    deleteInvitation,
    setInvitationRole,
  } = useUser();
  const { data: skills, isLoading: skillsAreLoading } = useSkills();
  const { isLoading: propertiesAreLoading, data: properties } = useProperties({
    showDeleted: true,
  });
  const { accountId, memberId, userId, invitationId } = route.params ?? {};

  const operationType = invitationId
    ? OperationTypes.Invitation
    : OperationTypes.Member;
  const newInvitation = !(accountId || memberId || userId || invitationId);

  const { data: member, isLoading: memberIsLoading } = useQuery(
    ['accountMember', accountId, memberId],
    () => getAccountMember(accountId, memberId),
    {
      enabled: Boolean(accountId && memberId),
    },
  );

  const [propertyAccessType, setPropertyAccessType] = useState(
    PropertyAccessType.All,
  );

  useEffect(() => {
    if (member?.allowedProperties.length === 0) {
      setPropertyAccessType(PropertyAccessType.All);
    } else {
      setPropertyAccessType(PropertyAccessType.Specific);
    }
  }, [member?.allowedProperties.length]);

  const { data: invitation } = useQuery(
    ['invitation', invitationId],
    () => getInvitation(invitationId),
    {
      enabled: Boolean(invitationId),
    },
  );

  const {
    mutate: EditAccountMemberMutate,
    isLoading: editAccountMemberIsLoading,
  } = useMutation(editAccountMember, {
    onSuccess: async () => {
      toast.show(`You have succesfully modified the user`);
      await queryClient.invalidateQueries('accountMembers');
      await queryClient.invalidateQueries([
        'accountMember',
        accountId,
        memberId,
      ]);
      navigation.goBack();
    },
  });

  const { mutate: postInvitationMutation, isLoading: postInvitationIsLoading } =
    useMutation(postInvitation, {
      onSuccess: async () => {
        toast.show('You have succesfully sent an invitation');
        await queryClient.invalidateQueries('pendingInvitations');
        navigation.goBack();
      },
    });

  const { mutate: setInvitationRoleMutate } = useMutation(setInvitationRole, {
    onSuccess: async () => {
      toast.show(`You have succesfully modified the invitation's role`);
      await queryClient.invalidateQueries('pendingInvitations');
      await queryClient.invalidateQueries(['invitation', invitationId]);
      navigation.goBack();
    },
  });

  const {
    mutate: deleteInvitationMutation,
    isLoading: deleteInvitationIsLoading,
  } = useMutation(deleteInvitation, {
    onSuccess: async () => {
      await queryClient.invalidateQueries('pendingInvitations');
      navigation.goBack();
    },
  });

  const {
    mutate: deleteAccountMemberMutation,
    isLoading: deleteAccountMemberIsLoading,
  } = useMutation(deleteAccountMember, {
    onSuccess: async () => {
      await queryClient.invalidateQueries(['accountMembers', accountId]);
      navigation.goBack();
    },
  });

  const isLoading = editAccountMemberIsLoading || postInvitationIsLoading;

  let initialData = { email: '', role: roles[0].id };
  if (memberId) {
    useLayoutEffect(() => {
      navigation.setOptions({
        headerTitle: member?.isDeleted
          ? 'Add back to Team'
          : 'Change team member role',
      });
    }, [member, navigation]);

    initialData = {
      id: member?.id,
      email: member?.email,
      role: member?.role,
      skillsetIds: member?.skillsetIds,
      isSkillsetLocked: Boolean(member?.isSkillsetLocked),
      hourlyRate: member?.hourlyRate,
      jobLimit: member?.jobLimit,
      allowedPropertyIds: member?.allowedProperties.map(
        property => property.id,
      ),
    };
  }
  if (invitationId) {
    useLayoutEffect(() => {
      navigation.setOptions({ headerTitle: 'Change invitation' });
    }, [navigation]);

    initialData = {
      id: invitation?.id,
      email: invitation?.email,
      role: invitation?.role,
    };
  }

  const save = async ({
    email,
    role,
    skillsetIds,
    isSkillsetLocked,
    hourlyRate,
    jobLimit,
    allowedPropertyIds,
  }) => {
    if (memberId && !member?.isDeleted) {
      await EditAccountMemberMutate({
        accountId,
        memberId,
        role,
        skillsetIds,
        isSkillsetLocked,
        hourlyRate,
        jobLimit,
        allowedPropertyIds:
          propertyAccessType === PropertyAccessType.All
            ? []
            : allowedPropertyIds,
      });
      return;
    }

    if (invitationId) {
      await setInvitationRoleMutate({ id: invitationId, role });
      return;
    }

    await postInvitationMutation({ name: 'New Member', email, role });
  };

  const handleDelete = () => {
    if (invitationId) {
      deleteInvitationMutation(invitation.id);
    } else {
      deleteAccountMemberMutation({ accountId, memberId });
    }
  };

  if (memberIsLoading || skillsAreLoading || propertiesAreLoading) {
    return <Spinner />;
  }

  if (member?.isDeleted) {
    return (
      <MemberRoleFormContainer
        initialValues={initialData}
        onSubmit={save}
        validationSchema={schema}
      >
        {({ values, setFieldValue, handleSubmit, isValid }) => (
          <>
            <ProfileSetting
              style={[styles.profileSetting]}
              hint="Email"
              value={
                <Input
                  disabled={!!member}
                  autoCapitalize="none"
                  style={styles.input}
                  placeholder="Email"
                  value={values.email}
                  onChangeText={value => setFieldValue('email', value)}
                />
              }
            />
            <ProfileSetting
              style={styles.profileSetting}
              hint="Role"
              value={
                <>
                  {roles.map(({ id, name, description }, index) => (
                    <CheckBox
                      key={id}
                      onChange={checked =>
                        setFieldValue(
                          'role',
                          checked ? id : roles[index + 1].id,
                        )
                      }
                      checked={values.role <= id}
                      disabled={
                        values.role < id || id === roles[roles.length - 1].id
                      }
                    >
                      <View style={styles.roleContent}>
                        <Text style={styles.roleName}>{name}</Text>
                        <Text style={styles.roleDescription}>
                          {description}
                        </Text>
                      </View>
                    </CheckBox>
                  ))}
                </>
              }
            />
            <SpinnerButton
              text="Add back to Team"
              style={styles.saveButton}
              onPress={handleSubmit}
              isLoading={isLoading}
              disabled={!isValid}
            />
            <Button
              style={styles.saveButton}
              appearance="outline"
              size="giant"
              onPress={() => navigation.goBack()}
            >
              Cancel
            </Button>
          </>
        )}
      </MemberRoleFormContainer>
    );
  }

  return (
    <MemberRoleFormContainer
      initialValues={initialData}
      onSubmit={save}
      validationSchema={schema}
    >
      {({ values, setFieldValue, handleSubmit, isValid }) => (
        <>
          <ProfileSetting
            style={[styles.profileSetting]}
            hint="Email"
            value={
              <Input
                disabled={!!member}
                autoCapitalize="none"
                placeholder="Email"
                value={values.email}
                onChangeText={value => setFieldValue('email', value)}
              />
            }
          />
          <ProfileSetting
            style={styles.profileSetting}
            hint="Role"
            value={
              <>
                {roles.map(({ id, name, description }, index) => (
                  <CheckBox
                    key={id}
                    onChange={checked =>
                      setFieldValue('role', checked ? id : roles[index + 1].id)
                    }
                    checked={values.role <= id}
                    disabled={
                      values.role < id || id === roles[roles.length - 1].id
                    }
                  >
                    <View style={styles.roleContent}>
                      <Text style={styles.roleName}>{name}</Text>
                      <Text style={styles.roleDescription}>{description}</Text>
                    </View>
                  </CheckBox>
                ))}
              </>
            }
          />
          {!newInvitation && operationType === OperationTypes.Member && (
            <>
              <ProfileSetting
                style={styles.profileSetting}
                hint="Skills"
                value={
                  <>
                    <SwitchInput
                      values={values}
                      keyName="isSkillsetLocked"
                      setFieldValue={setFieldValue}
                      text="Lock skills"
                      style={styles.lockSkillsSwitch}
                    />
                    <CheckboxesInput
                      propertyName="skillsetIds"
                      dataset={skills}
                    />
                  </>
                }
              />
              <ProfileSetting
                style={styles.profileSetting}
                hint="Hourly rate"
                value={
                  <Input
                    value={values.hourlyRate}
                    keyboardType="decimal-pad"
                    onChangeText={nextValue => {
                      setFieldValue('hourlyRate', nextValue);
                    }}
                  />
                }
              />
              <ProfileSetting
                style={styles.profileSetting}
                hint={`Allowed Jobs\nat a time`}
                value={
                  <RadioAndNumberInput
                    name="jobLimit"
                    value={values.jobLimit}
                    setFieldValue={setFieldValue}
                  />
                }
              />
              <ProfileSetting
                style={styles.profileSetting}
                hint={`Access\nJobs for`}
                value={
                  <>
                    <RadioGroup
                      selectedIndex={propertyAccessType}
                      onChange={index => setPropertyAccessType(index)}
                    >
                      <Radio>All Properties</Radio>
                      <Radio>Specific Properties</Radio>
                    </RadioGroup>
                    <CheckboxesInput
                      propertyName="allowedPropertyIds"
                      dataset={properties}
                      disabled={propertyAccessType === PropertyAccessType.All}
                    />
                  </>
                }
              />
            </>
          )}
          <SpinnerButton
            text={member || invitation ? 'Save' : 'Send Invitation'}
            style={styles.saveButton}
            onPress={handleSubmit}
            isLoading={isLoading}
            disabled={!isValid}
          />
          {(invitationId || (memberId && !member?.isDeleted)) && (
            <>
              <SpinnerButton
                text={
                  operationType === OperationTypes.Invitation
                    ? 'Delete'
                    : 'Remove from team'
                }
                style={styles.saveButton}
                onPress={() => setShowConfirm(true)}
                isLoading={
                  deleteInvitationIsLoading || deleteAccountMemberIsLoading
                }
                disabled={memberId && memberId === userId}
              />
              <Confirm
                show={showConfirm}
                onCancel={() => setShowConfirm(false)}
                onConfirm={handleDelete}
                text={`Are you sure you want to remove this ${operationType}?`}
              />
            </>
          )}
          <Button
            style={styles.saveButton}
            appearance="outline"
            size="giant"
            onPress={() => navigation.goBack()}
          >
            Cancel
          </Button>
        </>
      )}
    </MemberRoleFormContainer>
  );
};

const themedStyle = StyleService.create({
  profileSetting: {
    padding: 16,
  },
  saveButton: {
    marginHorizontal: 16,
    marginBottom: 16,
  },
  heading: {
    fontSize: 22,
    margin: 16,
  },
  radio: {
    margin: 2,
  },
  roleContent: {
    paddingTop: 20,
    flexDirection: 'column',
  },
  roleName: {
    fontWeight: 'bold',
  },
  roleDescription: {
    color: 'gray',
  },
  lockSkillsSwitch: {
    marginLeft: 10,
    marginBottom: 30,
  },
});

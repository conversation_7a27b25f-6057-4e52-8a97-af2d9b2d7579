import React from 'react';
import { StyleService, Text, useStyleSheet } from '@ui-kitten/components';
import Stack from '~/components/HouseKeepingComponents/ui/Stack';
import { formatDateString } from '~/hk-helpers';
import { HKExtendedJob } from '~/types';

type Props = {
  job: HKExtendedJob;
};

const DatesAndPropertyInfo = ({ job }: Props) => {
  const styles = useStyleSheet(themedStyles);
  const { booking, property } = job;

  return (
    <Stack style={styles.container}>
      <Text>
        Reservation Dates: {formatDateString(booking.arrival)} -{' '}
        {formatDateString(booking.departure)}
      </Text>
      <Text>
        Property: {property.property_name}, {property.propertyAddress}
      </Text>
    </Stack>
  );
};

export default DatesAndPropertyInfo;

const themedStyles = StyleService.create({
  container: {
    gap: 4,
  },
});

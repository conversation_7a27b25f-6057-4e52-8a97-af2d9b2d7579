import React, { ComponentProps, useState } from 'react';
import { Button, StyleService, useStyleSheet } from '@ui-kitten/components';
import Stack from '~/components/HouseKeepingComponents/ui/Stack';
import SharePDFButton from '~/components/HouseKeepingComponents/Job/SharePDFButton';

type Props = ComponentProps<typeof Stack> & {
  onClickFinish: () => void;
  disabled: boolean;
  pdfReportPath: string;
};

const ShareAndFinishButtons = ({
  onClickFinish,
  disabled,
  pdfReportPath,
  style,
  ...props
}: Props) => {
  const styles = useStyleSheet(themedStyles);
  const [pdfReportShared, setPdfReportShared] = useState(false);

  const handleClickSharePDFReport = () => setPdfReportShared(true);

  return (
    <Stack style={[styles.container, style]} {...props}>
      <SharePDFButton
        url={pdfReportPath}
        onClickShare={handleClickSharePDFReport}
        disabled={disabled}
        shared={pdfReportShared}
      />
      <Button
        size="large"
        style={styles.finishButton}
        onPress={onClickFinish}
        disabled={!pdfReportShared || disabled}
      >
        Finish Job
      </Button>
    </Stack>
  );
};

export default ShareAndFinishButtons;

const themedStyles = StyleService.create({
  container: {
    gap: 12,
    paddingBottom: 12,
    backgroundColor: 'white',
  },
  finishButton: {
    width: '100%',
  },
});

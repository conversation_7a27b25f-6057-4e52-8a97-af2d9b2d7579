import React, { useState, useCallback, useEffect } from 'react';
import { Text } from '@ui-kitten/components';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { StackScreenProps } from '~node_modules/@react-navigation/stack/lib/typescript/src';
import ChecklistPage from '~components/HouseKeepingComponents/AssignRolesForm/ChecklistPage';
import {
  MyInvoicesParamList,
  HKExtendedJob,
  HKJob,
  HKJobSection,
  HKSection,
  HKTask,
} from '~types';

import { whoAmI, pdfReportPath } from '~hk-helpers';
import {
  addJob,
  completeJobSection,
  fetchItems,
  fetchJob,
  fetchSections,
  startChecklist,
  finishJob,
} from '~hk-api';

type Props = StackScreenProps<MyInvoicesParamList, 'ChecklistSetupScreen'>;

const ChecklistSetupScreen = ({ route }: Props) => {
  const {
    jobId,
    performer,
    token: tokenFromParams,
    keeper: keeperIdFromParams,
  } = route.params || {};

  const [token, setToken] = useState<string | null>(null);
  const [keeperId, setKeeperId] = useState<string | null>(null);
  const [sections, setSections] = useState<HKSection[]>([]);
  const [tasks, setTasks] = useState<HKTask[]>([]);
  const [job, setJob] = useState<HKExtendedJob | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const helperView = performer?.[0] === 'helper';
  const user = whoAmI(job, helperView);
  const { performType } = job || {};

  useEffect(() => {
    (async () => {
      if (token && keeperId) {
        return;
      }

      setToken(await AsyncStorage.getItem('token'));
      setKeeperId(await AsyncStorage.getItem('keeper'));
    })();
  }, [token, keeperId]);

  useEffect(() => {
    if (!tokenFromParams || !keeperIdFromParams) {
      setToken('77af4753aca4fd8823ddcb20d87eab3f');
      setKeeperId('77');
      return;
    }
    const init = async () => {
      AsyncStorage.setItem('token', tokenFromParams);
      AsyncStorage.setItem('keeper', keeperIdFromParams);
      setToken(tokenFromParams);
      setKeeperId(keeperIdFromParams);
    };

    init();
  }, [tokenFromParams, keeperIdFromParams]);

  const updateJob = useCallback(async () => {
    if (!token || !keeperId) {
      return;
    }

    const res: HKExtendedJob = await fetchJob(jobId, keeperId, token);
    if (!res || !res.leaderPerforms) {
      return;
    }

    setJob(res);
  }, [jobId, keeperId, token]);

  useEffect(() => {
    (async () => {
      if (!token || !keeperId) {
        return;
      }
      setIsLoading(true);
      await fetchSections(keeperId, token).then(data => setSections(data));
      await fetchItems(keeperId, token).then(data => setTasks(data));
      await updateJob();
      setIsLoading(false);
    })();
  }, [helperView, jobId, keeperId, token, updateJob]);

  if (!token || !keeperId) {
    return <Text style={{ color: 'red', padding: 30 }}>Access Denied.</Text>;
  }

  const handleClickFinish = () => finishJob(jobId, keeperId, token);

  const handleSaveAssignRolesForm = async (values: HKJob) => {
    setJob(await addJob({ ...values, jobId }, keeperId, token));
  };

  const handleClickPDFReport = () =>
    window.open(pdfReportPath('en', jobId, keeperId, token), '_blank');

  const handleStartChecklist = async () => {
    if (!job || !user?.performing) {
      return;
    }

    const updatedJob = await startChecklist(
      job,
      user?.performing,
      keeperId,
      token,
    );
    setJob(updatedJob);
  };

  const handleCompleteSection = async (
    jobSection: Omit<HKJobSection, 'completed_at'>,
  ) => {
    const updatedJob = await completeJobSection(jobSection, keeperId, token);
    setJob(updatedJob);
  };

  return (
    <ChecklistPage
      job={job}
      user={user}
      isLoading={isLoading}
      sections={sections}
      onStartChecklist={handleStartChecklist}
      onCompleteSection={handleCompleteSection}
      tasks={tasks}
      onClickFinish={handleClickFinish}
      onClickPDFReport={handleClickPDFReport}
      pdfReportPath={pdfReportPath('en', jobId, keeperId, token)}
      onSaveAssignRolesForm={handleSaveAssignRolesForm}
      updateJob={updateJob}
      canEditRoles
      onlyAssigning={performType === 'assigned'}
    />
  );
};

export default ChecklistSetupScreen;

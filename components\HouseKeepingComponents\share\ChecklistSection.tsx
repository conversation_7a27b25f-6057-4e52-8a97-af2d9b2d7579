/* eslint-disable camelcase */
import React, { useMemo, useState } from 'react';
import {
  CheckBox,
  Icon,
  StyleService,
  Text,
  useStyleSheet,
} from '@ui-kitten/components';
import * as Yup from 'yup';
import { useToast } from 'react-native-toast-notifications';
import { View } from 'react-native';
import theme from '~/theme.json';
import {
  HKSection,
  HKChecklist,
  HKTask,
  HKExtendedJob,
  HKUser,
  HKJobSection,
} from '~types';
import ChecklistSectionHeader from '~components/HouseKeepingComponents/AssignRolesForm/ChecklistSectionHeader';
import { formatDateWithTimezone, intervalToHuman } from '~hk-helpers';
import SpinnerButton from '~components/SpinnerButton';
import { ClockOutlineIcon, PinIcon } from '~components/Icon';
import Badge from '~components/HouseKeepingComponents/ui/Badge';
import { Field, Formik, FormikValues } from '~node_modules/formik/dist';
import Stack from '~components/HouseKeepingComponents/ui/Stack';
import Flex from '~/components/HouseKeepingComponents/ui/Flex';
import { dummyAddress } from '~/hk-dummyData';
import { TimerClockIcon } from '~/components/HouseKeepingComponents/ui/MaterialIcons';

type Props = {
  section: HKSection;
  checklist: HKChecklist;
  tasks: HKTask[];
  job?: HKExtendedJob | null;
  user?: HKUser | null;
  onComplete?: (
    jobSection: Omit<HKJobSection, 'completed_at'>,
  ) => Promise<void>;
  disabled?: boolean;
};

type FormValues = {
  taskIds: string[];
};

const ChecklistSection = ({
  job,
  user,
  section,
  checklist,
  tasks,
  onComplete,
  disabled,
}: Props) => {
  console.log('job', job);
  const styles = useStyleSheet(themedStyles);
  const toast = useToast();
  const jobSection =
    job?.jobSections.find(
      js => js.section_id === section.id && js.checklist === checklist,
    ) || null;
  const [submitting, setSubmitting] = useState(false);
  // const { ref, entry } = useIntersection({ threshold: 1 });
  const { id: sectionId } = section;
  const jobSectionId = jobSection?.id;
  const completed = jobSection?.status === 'completed';
  const address = jobSection?.location?.address || dummyAddress;
  const timezone = jobSection?.location?.timezone || 'America/Los_Angeles';
  // const completedTasks = jobSection?.items || [];
  const completed_at = jobSection?.completed_at;
  const completedAtWithTimezone = formatDateWithTimezone(
    completed_at,
    timezone,
  );
  const first_section_started_at = job?.jobSections[0]?.started_at;
  const started_at = jobSection?.started_at;
  const completedIn = intervalToHuman(started_at, completed_at);
  const totalTimeElapsed = intervalToHuman(
    first_section_started_at,
    completed_at,
  );
  const showTotal =
    first_section_started_at && first_section_started_at !== started_at;

  const schema = useMemo(() => {
    const allTaskIds = tasks.map(task => task.id);

    return Yup.object().shape({
      taskIds: Yup.array()
        .of(Yup.string())
        .required('Please complete all tasks.')
        .test('all-tasks-selected', 'Please complete all tasks.', value => {
          if (!value || !Array.isArray(value)) {
            return false;
          }
          return allTaskIds.every(taskId => value.includes(taskId));
        }),
    });
  }, [tasks]);

  const onSubmit = async ({ taskIds }: FormValues) => {
    if (!job || !user) {
      return;
    }
    setSubmitting(true);

    const { jobId } = job;
    const { performing } = user;

    if (!jobId || !jobSectionId || !started_at) {
      return;
    }

    setSubmitting(false);
    onComplete?.({
      id: jobSectionId,
      jobId,
      section_id: sectionId,
      checklist: performing,
      items: taskIds as string[],
      status: 'completed',
      started_at,
      location: null,
    })
      .then(() => {
        toast.show('Section completed.');
      })
      .catch(() => {
        toast.show('Could not save section. Please try again later.', {
          type: 'danger',
        });
      })
      .finally(() => {
        setSubmitting(false);
      });
  };

  const checklistSectionHeader = (
    <ChecklistSectionHeader
      title={section.name}
      description="Please complete all tasks before completing the section."
      first_section_started_at={first_section_started_at}
      started_at={started_at}
      completed_at={completed_at}
      timezone={timezone}
    />
  );

  const initValues = {
    taskIds: jobSection?.items || [],
  };

  return (
    <Formik
      initialValues={initValues}
      onSubmit={onSubmit}
      enableReinitialize
      validationSchema={schema}
      validateOnMount
    >
      {({ handleSubmit, isValid }) => (
        <Stack
          style={[styles.checklistContainer, completed && styles.completed]}
        >
          {checklistSectionHeader}
          <Field>
            {({
              form: { values, setFieldValue, errors, touched },
            }: FormikValues) => {
              const localValues = values.taskIds;
              return (
                <View style={styles.checkboxContainer}>
                  {tasks.map(({ id, name }) => (
                    <CheckBox
                      key={id}
                      checked={localValues.includes(id)}
                      style={[
                        styles.checkbox,
                        !completed &&
                          localValues.includes(id) &&
                          styles.checked,
                      ]}
                      onChange={() => {
                        const currentValues = [...localValues];
                        const taskIndex = currentValues.indexOf(id);

                        if (taskIndex === -1) {
                          currentValues.push(id);
                        } else {
                          currentValues.splice(taskIndex, 1);
                        }

                        setFieldValue('taskIds', currentValues);
                      }}
                      disabled={disabled}
                    >
                      {name}
                    </CheckBox>
                  ))}
                  {!disabled && (touched.taskIds || errors.taskIds) && (
                    <Text style={styles.error}>{errors.taskIds}</Text>
                  )}
                </View>
              );
            }}
          </Field>
          {completed ? (
            <Flex style={styles.completedBadges}>
              <Icon
                name="checkmark-circle-2"
                fill={theme['color-success-600']}
                width={70}
                style={styles.icon}
              />
              <Stack style={styles.badgeGroup}>
                {completed_at && (
                  <Badge
                    status="success"
                    size="large"
                    style={styles.badge}
                    leftSection={ClockOutlineIcon}
                  >
                    {completedAtWithTimezone}
                  </Badge>
                )}
                {completedIn && (
                  <Badge
                    status="success"
                    size="large"
                    style={styles.badge}
                    leftSection={TimerClockIcon}
                  >
                    Section: {completedIn}
                  </Badge>
                )}
                {showTotal && totalTimeElapsed && (
                  <Badge
                    status="success"
                    size="large"
                    style={styles.badge}
                    leftSection={TimerClockIcon}
                  >
                    Total: {totalTimeElapsed}
                  </Badge>
                )}
                {address && (
                  <Badge
                    size="large"
                    style={styles.badge}
                    leftSection={PinIcon}
                  >
                    {address?.properties?.name_preferred}
                  </Badge>
                )}
              </Stack>
            </Flex>
          ) : (
            <SpinnerButton
              text="Complete Section"
              size="large"
              onPress={() => handleSubmit()}
              isLoading={submitting}
              disabled={!isValid || disabled}
            />
          )}
        </Stack>
      )}
    </Formik>
  );
};

export default ChecklistSection;

const themedStyles = StyleService.create({
  checklistContainer: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
    elevation: 2,
    padding: 12,
    borderRadius: 4,
  },
  completedBadges: {
    gap: 12,
    padding: 5,
    alignItems: 'center',
    flexWrap: 'nowrap',
  },
  badgeGroup: {
    flex: 1,
  },
  icon: {
    flexShrink: 0,
    height: '100%',
    aspectRatio: 1,
  },
  badge: {
    paddingLeft: 5,
  },
  checklistItemsGroup: {
    marginTop: 12,
  },
  checkboxContainer: {
    gap: 16,
  },
  checkbox: {
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#dee2e6',
    padding: 16,
    borderRadius: 8,
    alignItems: 'flex-start',
  },
  error: {
    color: 'red',
    marginTop: 8,
  },
  completed: {
    backgroundColor: 'color-success-transparent-100',
  },
  checked: {
    borderColor: 'color-primary-default',
  },
});

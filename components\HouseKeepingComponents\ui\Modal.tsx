import React, { ReactNode } from 'react';
import {
  View,
  Modal as NativeModal,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  ModalProps,
  ViewStyle,
} from 'react-native';
import { StyleService, useStyleSheet } from '@ui-kitten/components';
import ModalHeader from '~/components/HouseKeepingComponents/ui/ModalHeader';

const { height: SCREEN_HEIGHT } = Dimensions.get('screen');

type Props = {
  visible: boolean;
  onClose: () => void;
  title: string;
  children: ReactNode;
  style?: ViewStyle;
  stickyHeader?: boolean;
} & Omit<ModalProps, 'visible' | 'children'>;

const Modal = ({
  visible,
  onClose,
  title,
  children,
  style,
  stickyHeader = false,
  ...modalProps
}: Props) => {
  const styles = useStyleSheet(themedStyles);

  const handleBackdropPress = () => {
    onClose();
  };

  if (!visible) {
    return null;
  }

  return (
    <NativeModal
      visible={visible}
      transparent
      animationType="none"
      onRequestClose={onClose}
      {...modalProps}
    >
      <View style={styles.container}>
        <TouchableOpacity
          style={styles.backdrop}
          activeOpacity={1}
          onPress={handleBackdropPress}
        />
        <View style={[styles.modal, style]}>
          <ModalHeader
            title={title}
            onClose={onClose}
            stickyHeader={stickyHeader}
          />
          <ScrollView
            style={[styles.content, stickyHeader && styles.stickyHeaderContent]}
          >
            {children}
          </ScrollView>
        </View>
      </View>
    </NativeModal>
  );
};

const themedStyles = StyleService.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  backdrop: {
    position: 'absolute',
    inset: 0,
  },
  modal: {
    margin: 16,
    borderRadius: 4,
    overflow: 'hidden',
    backgroundColor: 'white',
    maxHeight: SCREEN_HEIGHT - 32,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  stickyHeaderContent: {
    paddingTop: 72,
  },
});

export default Modal;

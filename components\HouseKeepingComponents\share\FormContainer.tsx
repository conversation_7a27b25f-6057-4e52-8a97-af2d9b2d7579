import React, { ReactElement } from 'react';
import { Formik, FormikConfig, FormikHelpers, FormikValues } from 'formik';
import KeyboardAvoidingView from '~components/KeyboardAvoidingView';

type FormCotaninerProps<T> = {
  initialValues: T;
  onSubmit: (
    values: T,
    formikHelpers: FormikHelpers<T>,
  ) => void | Promise<void>;
  validationSchema: FormikConfig<T>['validationSchema'];
  children: (props: {
    values: T;
    setFieldValue: (
      field: string,
      value: unknown,
      shouldValidate?: boolean,
    ) => void;
    handleSubmit: () => void;
    isValid: boolean;
  }) => ReactElement;
};

const FormContainer = <T extends FormikValues>({
  initialValues,
  onSubmit,
  validationSchema,
  children,
}: FormCotaninerProps<T>): ReactElement => (
  <KeyboardAvoidingView>
    <Formik
      initialValues={initialValues}
      onSubmit={onSubmit}
      enableReinitialize
      validationSchema={validationSchema}
      validateOnMount
    >
      {children}
    </Formik>
  </KeyboardAvoidingView>
);

export default FormContainer;

import React from 'react';
import {
  Button,
  StyleService,
  Text,
  useStyleSheet,
} from '@ui-kitten/components';
import Group from '~components/HouseKeepingComponents/ui/Group';
import { CloseIcon } from '~components/Icon';

type Props = {
  title: string;
  onClose: () => void;
  stickyHeader?: boolean;
};

const ModalHeader = ({ title, onClose, stickyHeader }: Props) => {
  const styles = useStyleSheet(themedStyles);

  return (
    <Group style={[styles.header, stickyHeader && styles.stickyHeader]}>
      <Text category="s1" style={styles.title}>
        {title}
      </Text>
      <Button
        appearance="ghost"
        size="small"
        onPress={onClose}
        style={styles.closeButton}
        accessoryLeft={CloseIcon}
      />
    </Group>
  );
};

export default ModalHeader;

const themedStyles = StyleService.create({
  header: {
    justifyContent: 'space-between',
    padding: 16,
    paddingRight: 8,
  },
  title: {
    fontSize: 18,
  },
  stickyHeader: {
    backgroundColor: 'white',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1,
  },
  closeButton: {
    width: 36,
    height: 36,
    paddingVertical: 0,
    paddingHorizontal: 0,
  },
});

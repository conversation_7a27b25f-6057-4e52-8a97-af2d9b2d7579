import React, { ReactElement } from 'react';
import { Card } from '@ui-kitten/components';
import { StyleSheet, View } from 'react-native';
import { differenceInDays } from 'date-fns';
import { useQuery } from 'react-query';
import { Task, TaskAssignment } from '~types';
import { recurringIntervals } from '~dummyData';
import useUser from '~api/useUser';
import { priorityIsUrgentOrAnytime } from '~helpers';
import Spinner from './Spinner';
import useTimezone from '~hooks/useTimezone';
import TaskCardTextContent from './TaskCardComponents/TaskCardTextContent';
import TaskCardHeader from './TaskCardComponents/TaskCardHeader';

type Props = Task & {
  id: number;
  children?: ReactElement;
  onPress?: () => void;
  showDetails?: boolean;
  isAssignment?: boolean;
  copiedForToday?: boolean;
  repetitionNumber?: number;
  taskAssignment?: TaskAssignment;
  deletedAt: string;
};

const TaskCard = ({
  children,
  id,
  media,
  thumbnail,
  description,
  property,
  preferredDate,
  requiredSkills,
  priority,
  onPress = () => {},
  showDetails,
  isAssignment,
  creatorUserId,
  recurring,
  repetitionNumber,
  createdAt,
  copiedForToday,
  blockedUsers,
  taskAssignment,
  propertyAvailability,
  deletedAt,
}: Props): ReactElement => {
  const { zonedTimeToUtc } = useTimezone();
  const { getUser, getAccountMembers } = useUser();
  const { data: user } = useQuery('user', () => getUser());
  const accountId = user?.accounts[0].id;
  const { isLoading, data: accountMembers = [] } = useQuery(
    ['accountMembers', accountId],
    () => getAccountMembers(accountId!, true),
    { enabled: !!accountId },
  );

  const overdueInDays = () => {
    const diffInDays = differenceInDays(
      zonedTimeToUtc(new Date()),
      new Date(preferredDate),
    );

    if (diffInDays > 0 && !priorityIsUrgentOrAnytime(priority)) {
      return diffInDays;
    }
    return 0;
  };

  const overdue = overdueInDays();

  const recurringId = Number(recurring);

  const creatorUser = accountMembers.find(
    ({ id: userId }) => userId === creatorUserId,
  );

  if (isLoading || !creatorUser) {
    return <Spinner />;
  }

  const recurringIntervalName = recurringIntervals.find(
    item => item.id === recurringId,
  )?.name;

  if (!recurringIntervalName) {
    return <Spinner />;
  }

  return (
    <View style={styles.container}>
      <Card
        header={
          <TaskCardHeader
            id={id}
            overdue={overdue}
            priority={priority}
            media={media}
            showDetails={showDetails}
            thumbnail={thumbnail}
            repetitionNumber={repetitionNumber}
            blockedUsers={blockedUsers}
            finishedAt={taskAssignment?.finishedAt}
            deletedAt={deletedAt}
          />
        }
        onPress={onPress}
      >
        <TaskCardTextContent
          description={description}
          priority={priority}
          isAssignment={isAssignment}
          copiedForToday={copiedForToday}
          preferredDate={preferredDate}
          property={property}
          recurring={recurring}
          requiredSkills={requiredSkills}
          createdAt={createdAt}
          creatorUser={creatorUser}
          overdue={overdue}
          acceptedByUser={taskAssignment?.user}
          acceptedOn={taskAssignment?.createdAt}
          finishedAt={taskAssignment?.finishedAt}
          showDetails={showDetails}
          propertyAvailability={propertyAvailability}
        />
        {children}
      </Card>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: 16,
    marginBottom: 16,
  },
});

export default TaskCard;

import React from 'react';
import { Button, StyleService, useStyleSheet } from '@ui-kitten/components';
import Flex from '~/components/HouseKeepingComponents/ui/Flex';
import { UnlockIcon, PeopleOutlineIcon, ShareIcon } from '~/components/Icon';

type Props = {
  onClickRoles: (() => void) | null;
  onClickReport: (() => void) | null;
  onClickPropertyAccess: (() => void) | null;
};

const HeaderButtons = ({
  onClickPropertyAccess,
  onClickRoles,
  onClickReport,
}: Props) => {
  const styles = useStyleSheet(themedStyles);

  return (
    <Flex style={styles.container}>
      {onClickPropertyAccess && (
        <Button
          style={styles.button}
          onPress={onClickPropertyAccess}
          appearance="outline"
          accessoryLeft={UnlockIcon}
        >
          Access
        </Button>
      )}
      {onClickRoles && (
        <Button
          style={styles.button}
          onPress={onClickRoles}
          appearance="outline"
          accessoryLeft={PeopleOutlineIcon}
        >
          Roles
        </Button>
      )}
      {onClickReport && (
        <Button
          style={styles.button}
          onPress={onClickReport}
          appearance="outline"
          accessoryLeft={ShareIcon}
        >
          Report
        </Button>
      )}
    </Flex>
  );
};

export default HeaderButtons;

const themedStyles = StyleService.create({
  container: {
    gap: 10,
  },
  button: {
    flex: 1,
    paddingVertical: 4,
    minHeight: 0,
  },
});

/* eslint-disable camelcase */
import React from 'react';
import { View } from 'react-native';
import { StyleService, Text, useStyleSheet } from '@ui-kitten/components';
import { ClockOutlineIcon } from '~/components/Icon';
import ElapsedTime from '~components/HouseKeepingComponents/share/ElapsedTime';
import { formatDateWithTimezone } from '~/hk-helpers';
import Group from '~/components/HouseKeepingComponents/ui/Group';
import Badge from '~/components/HouseKeepingComponents/ui/Badge';
import { TimerClockIcon } from '~/components/HouseKeepingComponents/ui/MaterialIcons';

type Props = {
  title: string;
  description: string;
  first_section_started_at?: string;
  started_at?: string;
  completed_at?: string;
  timezone: string;
};

const ChecklistSectionHeader = ({
  title,
  description,
  first_section_started_at,
  started_at,
  completed_at,
  timezone,
}: Props) => {
  const styles = useStyleSheet(themedStyles);
  const startedAtWithTimezone = formatDateWithTimezone(started_at, timezone);
  const showTotal =
    first_section_started_at && first_section_started_at !== started_at;

  return (
    <View style={styles.container}>
      <Text style={styles.title}>{title}</Text>
      <Text>{description}</Text>
      {started_at && !completed_at && (
        <Group style={styles.badgeGroup}>
          <Badge
            size="large"
            style={styles.icon}
            leftSection={ClockOutlineIcon}
          >
            {startedAtWithTimezone}
          </Badge>
          <Badge size="large" style={styles.icon} leftSection={TimerClockIcon}>
            Section: <ElapsedTime startTime={started_at} />
          </Badge>
          {showTotal && (
            <Badge
              size="large"
              style={styles.icon}
              leftSection={TimerClockIcon}
            >
              Total: <ElapsedTime startTime={first_section_started_at} />
            </Badge>
          )}
        </Group>
      )}
    </View>
  );
};

export default ChecklistSectionHeader;

const themedStyles = StyleService.create({
  badgeGroup: {
    marginVertical: 8,
    gap: 8,
  },
  container: {
    padding: 5,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  icon: {
    paddingLeft: 5,
  },
});

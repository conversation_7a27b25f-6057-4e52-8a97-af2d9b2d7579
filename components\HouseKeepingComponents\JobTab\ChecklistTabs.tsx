import React, { Dispatch, useEffect } from 'react';
import {
  Card,
  StyleService,
  Tab,
  TabBar,
  useStyleSheet,
} from '@ui-kitten/components';
import { View } from 'react-native';
import ChecklistSections from '~/components/HouseKeepingComponents/share/ChecklistSections';
import { checklistLabel, enabledTabs } from '~/hk-helpers';
import {
  HKChecklist,
  HKExtendedJob,
  HKJobSection,
  HKSection,
  HKTask,
  HKUser,
} from '~/types';
import usePerformOptions from '~/hooks/usePerformOptions';
import StartChecklistButton from '~/components/HouseKeepingComponents/JobTab/StartChecklistButton';
import <PERSON>ForOtherPerson from '~/components/HouseKeepingComponents/Job/LinkForOtherPerson';

type Props = {
  sections: HKSection[];
  job: HKExtendedJob;
  user: HKUser | null;
  tasks: HKTask[];
  performing: HKChecklist | null;
  updateJob: () => void;
  onStartChecklist: () => Promise<void>;
  onCompleteSection: (
    jobSection: Omit<HKJobSection, 'completed_at'>,
  ) => Promise<void>;
  isLeader: boolean;
  selectedTab: HKChecklist | null;
  setSelectedTab: Dispatch<React.SetStateAction<HKChecklist>>;
  activeSection: HKSection | null;
  onlyAssigning?: boolean;
};

const ChecklistTabs = ({
  sections,
  job,
  user,
  tasks,
  performing,
  updateJob,
  onStartChecklist,
  onCompleteSection,
  isLeader,
  selectedTab,
  setSelectedTab,
  activeSection,
  onlyAssigning,
}: Props) => {
  const styles = useStyleSheet(themedStyles);
  const allPerformOptions = usePerformOptions({ performPersonally: true });

  const checklistStarted = job.jobSections.some(
    ({ checklist }) => checklist === performing,
  );

  const performOptions = allPerformOptions
    .filter(({ checklist }) => enabledTabs(performing).includes(checklist))
    .map(({ icon, checklist, description }) => ({
      icon,
      label: checklistLabel(job, checklist),
      checklist,
      description,
    }));

  useEffect(() => {
    if (!performing) {
      return;
    }

    setSelectedTab(performing);
  }, [performing, setSelectedTab]);

  const tabIndex = performOptions.findIndex(
    ({ checklist }) => checklist === selectedTab,
  );

  const handleTabSelect = (index: number) => {
    const selectedChecklist = performOptions[index]?.checklist || null;
    updateJob();
    setSelectedTab(selectedChecklist);
  };

  console.log('activeSection', activeSection);

  const renderTabContent = (checklist: HKChecklist) => (
    <View style={styles.gapContainer}>
      {(onlyAssigning ||
        (isLeader && !!performing && checklist !== performing)) && (
        <Card disabled style={styles.link}>
          <LinkForOtherPerson job={job} checklist={checklist} />
        </Card>
      )}
      {checklist === performing && !checklistStarted && !onlyAssigning && (
        <StartChecklistButton onStartChecklist={onStartChecklist} />
      )}
      <ChecklistSections
        job={job}
        user={user}
        tasks={tasks.filter(task => task[checklist])}
        sections={sections}
        checklist={checklist}
        onCompleteSection={onCompleteSection}
        activeSection={activeSection}
        onlyAssigning={onlyAssigning}
      />
    </View>
  );

  return (
    <View style={styles.container}>
      <TabBar selectedIndex={tabIndex} onSelect={handleTabSelect}>
        {performOptions.map(({ icon, checklist }) => (
          <Tab
            key={checklist}
            title={checklistLabel(job, checklist)}
            icon={<View>{icon}</View>}
            style={styles.tab}
          />
        ))}
      </TabBar>
      <View style={styles.contentContainer}>
        {selectedTab && renderTabContent(selectedTab)}
      </View>
    </View>
  );
};

export default ChecklistTabs;

const themedStyles = StyleService.create({
  container: {
    flex: 1,
    gap: 12,
  },
  contentContainer: {
    flex: 1,
  },
  link: {
    backgroundColor: 'color-info-transparent-100',
    paddingHorizontal: 0,
  },
  gapContainer: {
    gap: 12,
  },
  tab: {
    flexDirection: 'row',
    gap: 32,
  },
});

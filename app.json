{"expo": {"name": "Airteam App", "slug": "airteam", "version": "1.3.88", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "airteamapp", "userInterfaceStyle": "automatic", "splash": {"image": "./assets/images/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "updates": {"fallbackToCacheTimeout": 0, "url": "https://u.expo.dev/256b2636-6b6c-4348-af6f-8a0a5f40626d"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.airteam", "config": {"usesNonExemptEncryption": false}, "infoPlist": {"NSCameraUsageDescription": "This app uses the camera to allow users take photos or videos about maintenance issues and their completions.", "NSMicrophoneUsageDescription": "This app uses the microphone to allow users commenting on videos about maintenance issues and their completions.", "NSPhotoLibraryUsageDescription": "This app uses the photo library to allow users upload photos or videos about maintenance issues and their completions.", "LSApplicationQueriesSchemes": ["itms-apps"]}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}}, "web": {"bundler": "metro", "favicon": "./assets/images/favicon.png", "build": {"babel": {"include": ["@ui-kitten/components"]}}}, "description": "", "owner": "manystack", "plugins": [["@sentry/react-native/expo", {"organization": "manystack", "project": "vrc"}], ["expo-build-properties", {"android": {"compileSdkVersion": 34, "targetSdkVersion": 34, "buildToolsVersion": "34.0.0"}, "ios": {"deploymentTarget": "15.1"}}], "expo-asset", "expo-font", "expo-secure-store", "expo-web-browser"], "extra": {"eas": {"projectId": "256b2636-6b6c-4348-af6f-8a0a5f40626d"}}, "runtimeVersion": {"policy": "sdkVersion"}}}
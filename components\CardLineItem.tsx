import React, { ReactElement, ReactNode } from 'react';
import { StyleService, useStyleSheet, Icon, Text } from '@ui-kitten/components';
import { StyleProp, TextStyle, View } from 'react-native';
import { carrotColor } from '~constants/Colors';

type Props = {
  text: ReactNode;
  iconName: string;
  textStyle?: StyleProp<TextStyle>;
};

const CardLineItem = ({ text, iconName, textStyle }: Props): ReactElement => {
  const styles = useStyleSheet(themedStyles);

  return (
    <View style={styles.container}>
      <Icon style={styles.icon} fill={carrotColor} name={iconName} />
      <Text style={[styles.text, textStyle]}>{text as ReactElement}</Text>
    </View>
  );
};

const themedStyles = StyleService.create({
  container: {
    flexDirection: 'row',
    marginHorizontal: 16,
    marginVertical: 2,
  },
  icon: {
    width: 18,
    height: 18,
  },
  text: {
    paddingTop: 2,
    marginLeft: 16,
    fontSize: 12,
    fontWeight: 'bold',
    color: carrotColor,
  },
});

export default CardLineItem;

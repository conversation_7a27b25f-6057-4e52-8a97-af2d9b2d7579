import React, { ComponentProps } from 'react';
import { View } from 'react-native';
import {
  Text,
  StyleService,
  useStyleSheet,
  IconElement,
  IconProps,
} from '@ui-kitten/components';
import Group from '~/components/HouseKeepingComponents/ui/Group';
import theme from '~/theme.json';

type Props = ComponentProps<typeof View> & {
  status?: 'success' | 'info' | 'warning';
  variant?: 'filled' | 'outline' | 'clear';
  size?: 'tiny' | 'small' | 'medium' | 'large' | 'giant';
  circle?: boolean;
  leftSection?: (props: IconProps) => IconElement;
  rightSection?: (props: IconProps) => IconElement;
};

const getVariantStyle = (
  variant: Props['variant'],
  status: Props['status'] = 'info',
) => {
  const statusColor = statusColors[status];

  if (variant === 'filled') {
    return { backgroundColor: statusColor };
  }
  if (variant === 'outline') {
    return {
      backgroundColor: 'transparent',
      borderColor: statusColor,
      borderWidth: 1,
    };
  }
  return {};
};

const Badge = ({
  children,
  style: propStyle,
  variant = 'filled',
  status = 'info',
  size = 'medium',
  circle = false,
  leftSection: LeftSection,
  rightSection: RightSection,
  ...props
}: Props) => {
  const styles = useStyleSheet(themedStyles);

  const borderRadius = circle && { aspectRatio: 1 };
  const padding = !circle && paddings[size];

  return (
    <Group
      {...props}
      style={[
        styles.badge,
        getVariantStyle(variant, status),
        padding,
        borderRadius,
        propStyle,
      ]}
    >
      {LeftSection && <LeftSection width={24} fill="white" />}
      <Text
        category="s1"
        style={[styles.text, fontSizes[size]]}
        numberOfLines={2}
      >
        {children}
      </Text>
      {RightSection && <RightSection width={24} fill="white" />}
    </Group>
  );
};

export default Badge;

const themedStyles = StyleService.create({
  badge: {
    gap: 6,
    flexWrap: 'nowrap',
    borderRadius: 9999,
    flexShrink: 1,
    alignSelf: 'flex-start',
  },
  text: {
    color: 'white',
    textTransform: 'uppercase',
    fontWeight: 'bold',
  },
});

const fontSizes = StyleService.create({
  tiny: { fontSize: 9 },
  small: { fontSize: 10 },
  medium: { fontSize: 11 },
  large: { fontSize: 13 },
  giant: { fontSize: 16 },
});

const paddings = StyleService.create({
  tiny: { paddingHorizontal: 6, paddingVertical: 2 },
  small: { paddingHorizontal: 8, paddingVertical: 2 },
  medium: { paddingHorizontal: 10, paddingVertical: 2 },
  large: { paddingHorizontal: 12, paddingVertical: 2 },
  giant: { paddingHorizontal: 16, paddingVertical: 2 },
});

const statusColors = {
  success: theme['color-success-600'],
  info: theme['color-info-500'],
  warning: theme['color-warning-500'],
};

import React from 'react';
import { Button } from '@ui-kitten/components';
import { Platform } from 'react-native';
import { RWebShare } from 'react-web-share';
import CopyButton from '~/components/HouseKeepingComponents/ui/CopyButton';

const COPY_TIMEOUT = 5000;

type Props = {
  url: string;
  disabled: boolean;
  onClickShare: () => void;
  shared: boolean;
};

const SharePDFButton = ({ url, disabled, onClickShare, shared }: Props) => {
  if (Platform.OS !== 'web') {
    return (
      <RWebShare
        data={{
          text: 'PDF Report',
          url,
          title: 'PDF Report',
        }}
        sites={['whatsapp', 'copy', 'email', 'facebook', 'linkedin', 'twitter']}
        onClick={onClickShare}
      >
        <Button
          size="large"
          style={{ width: '100%' }}
          disabled={disabled}
          appearance={shared ? 'outline' : 'filled'}
        >
          Share PDF Report
        </Button>
      </RWebShare>
    );
  }

  return (
    <CopyButton
      href={url}
      timeout={COPY_TIMEOUT}
      onClickShare={onClickShare}
      disabled={disabled}
      style={{ width: '100%' }}
      copyText="Share PDF Report"
      copiedText="Link Copied to Clipboard"
      size="large"
      shareIcons={false}
    />
  );
};

export default SharePDFButton;

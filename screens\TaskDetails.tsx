import * as React from 'react';
import { useCallback, useLayoutEffect } from 'react';
import { useMutation, useQuery, useQueryClient } from 'react-query';
import { StackScreenProps } from '@react-navigation/stack';
import { StyleService, useStyleSheet } from '@ui-kitten/components';
import { Share, View } from 'react-native';
import _ from 'lodash';
import { useNavigationState } from '@react-navigation/native';
import { useToast } from 'react-native-toast-notifications';
import TaskCard from '../components/TaskCard';
import useTasks from '~api/useTasks';
import useUser from '~api/useUser';
import Spinner from '~components/Spinner';
import {
  Icon,
  IconProps,
  TopNavigationAction,
} from '~node_modules/@ui-kitten/components';
import { invalidateQueries } from '~helpers';
import HeaderButtonAction from '~components/HeaderButton';
import TaskCompletionCard from '~components/TaskCompletionCard';
import { TaskPriorities, UserRoles } from '~dummyData';
import TaskDetailActionButtons from '~components/TaskDetailActionButtons';
import { TaskAssignment } from '~types';
import CommentsCard from '~components/TaskComments';
import KeyboardAvoidingView from '~components/KeyboardAvoidingView';

type Props = StackScreenProps;

enum StateIndexes {
  AddTask = 3,
}

const staticJobDetailBaseUrl = process.env.STATIC_JOB_DETAIL_URL;

const onShare = async (message: string) => {
  await Share.share({
    message,
  });
};

const TaskDetails = ({ route, navigation }: Props): React.ReactElement => {
  const toast = useToast();
  const taskId = route?.params?.id;
  const copiedForToday = route?.params?.copiedForToday;
  const backnavScreenName = route?.params?.backnavScreenName;

  const navigationState = useNavigationState(state => state);

  const { index: stateIndex, routes: navigationStack } = navigationState;

  const closeTask = useCallback(() => {
    // If we're on the admin stack
    if (navigationStack[0]?.name === 'MoreScreen') {
      navigation.goBack();
      return;
    }

    // In the case we came from add task screen, there's 2 additional screens (add task + task setting) that we need to skip when we navigate backwards
    if (stateIndex === StateIndexes.AddTask) {
      navigation.navigate(navigationStack[navigationStack.length - 4]?.name);
      return;
    }

    // If we're on one of the the available tasks screen, recommended jobs tab's subcategory
    const recommendedTaskNavigationStackItem = navigationStack.find(
      navigationItem => navigationItem.name === 'RecommendedTaskListingScreen',
    );
    if (recommendedTaskNavigationStackItem) {
      navigation.navigate(
        recommendedTaskNavigationStackItem.name,
        recommendedTaskNavigationStackItem.params,
      );
      return;
    }

    if (backnavScreenName) {
      navigation.navigate(backnavScreenName);
      return;
    }

    navigation.navigate(navigationStack[0]?.name);
  }, [backnavScreenName, navigation, navigationStack, stateIndex]);

  const styles = useStyleSheet(themedStyles);

  const { getTask, acceptTask, deleteTask } = useTasks();
  const { data: task, isLoading: taskIsLoading } = useQuery(
    ['task', taskId],
    () => getTask(taskId, true, copiedForToday),
  );

  const { getUser } = useUser();
  const { data: currentUser, isLoading: curentUserIsLoading } = useQuery(
    'user',
    () => getUser(),
  );

  const currentUserRole = currentUser?.accounts[0].pivot.role;

  const queryClient = useQueryClient();

  const invalidateAfterAcceptOrDelete = () => {
    invalidateQueries(queryClient, [
      { key: 'task-assignments', options: { exact: true } },
      { key: 'tasks-created-by-me', options: {} },
      { key: 'tasks', options: {} },
    ]);
  };

  const jobDetailUrl = staticJobDetailBaseUrl + taskId;

  useLayoutEffect(() => {
    if (currentUserRole !== 2) {
      navigation.setOptions({
        headerLeft: headerLeft(closeTask),
      });
    }

    navigation.setOptions({
      headerRight: headerRight(jobDetailUrl),
    });
  }, [currentUserRole, navigation, closeTask, jobDetailUrl]);

  const { mutateAsync: acceptTaskMutate, isLoading: acceptTaskIsLoading } =
    useMutation(acceptTask, {
      onSuccess: async (response: {
        taskAssignment?: TaskAssignment;
        message: { text: string; denied?: boolean };
      }) => {
        const priority = response.taskAssignment?.task.priority;

        const {
          message: { text: error, denied },
        } = response;

        if (denied) {
          toast.show(error);

          return;
        }

        const text = [`The Job you accepted has been added to My Jobs.`];

        if (error) {
          text.push(error);
        }

        if (
          _.isNumber(priority) &&
          [TaskPriorities.Urgent, TaskPriorities.Anytime].includes(priority)
        ) {
          text.push(
            'You have 24 hours to complete it before it automatically gets removed from you.',
          );
        }
        toast.show(text.join('\n\n'));
        await invalidateAfterAcceptOrDelete();
        closeTask();
      },
    });

  const { mutateAsync: deleteTaskMutate } = useMutation(deleteTask, {
    onSuccess: async () => {
      await invalidateAfterAcceptOrDelete();
      closeTask();
    },
  });

  if (taskIsLoading || !task || curentUserIsLoading || !currentUser) {
    return <Spinner />;
  }

  const currentUserIsAdmin =
    currentUser?.accounts[0].pivot.role === UserRoles.Admin;
  const currentUserIsTheTaskOwner = task.creatorUserId === currentUser.id;

  const handleAcceptTask = () => acceptTaskMutate({ id: task.id });

  const handleDeleteTask = () => deleteTaskMutate(task.id);

  const userSkillIds = currentUser.skillsetIds;
  const { requiredSkillIds, id, comments } = task;

  const matchingSkills = _.intersection(userSkillIds, requiredSkillIds);

  return (
    <KeyboardAvoidingView contentContainerStyle={styles.container}>
      <TaskCard
        {...task}
        repetitionNumber={task.nextRepetitionNumber}
        showDetails
        copiedForToday={copiedForToday}
      >
        <TaskDetailActionButtons
          navigation={navigation}
          handleAcceptTask={handleAcceptTask}
          acceptTaskIsLoading={acceptTaskIsLoading}
          disabled={!matchingSkills.length}
          task={task}
          currentUserIsAdmin={currentUserIsAdmin}
          currentUserIsTheTaskOwner={currentUserIsTheTaskOwner}
          handleDeleteTask={handleDeleteTask}
          jobDetailUrl={jobDetailUrl}
        />
      </TaskCard>
      <CommentsCard taskId={id} comments={comments} />
      {task.completions?.map(taskAssignment => (
        <TaskCompletionCard
          key={taskAssignment.id}
          navigation={navigation}
          taskAssignment={taskAssignment}
          currentUserIsAdmin={currentUserIsAdmin}
          currentUserIsTheTaskOwner={taskAssignment.userId === currentUser.id}
        />
      ))}
    </KeyboardAvoidingView>
  );
};

const CloseTaskButton = ({ style }: IconProps) => (
  <View
    style={{
      flexDirection: 'row',
      alignItems: 'center',
      padding: 10,
    }}
  >
    <Icon style={style} name="close" />
  </View>
);

const CloseTaskAction = ({ onPress }: { onPress: () => void }) => (
  <TopNavigationAction icon={CloseTaskButton} onPress={onPress} />
);

const headerRight = (jobDetailUrl: string) => () =>
  (
    <HeaderButtonAction
      onPress={() => {
        onShare(jobDetailUrl);
      }}
      iconName="ios-share"
      iconPack="material"
    />
  );

const headerLeft = (closeTask: () => void) => () =>
  <CloseTaskAction onPress={closeTask} />;

const themedStyles = StyleService.create({
  container: {
    paddingVertical: 16,
  },
  actionButtonsContainer: {
    marginTop: 16,
  },
  button: {
    marginBottom: 16,
  },
});

export default TaskDetails;

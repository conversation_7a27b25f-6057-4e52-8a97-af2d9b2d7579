import React, { ReactElement, ReactNode } from 'react';
import {
  Text,
  useStyleSheet,
  StyleService,
  Button,
  Radio,
  RadioGroup,
} from '@ui-kitten/components';
import { View } from 'react-native';
import { Field, FormikValues } from 'formik';
import Group from '~/components/HouseKeepingComponents/ui/Group';
import Flex from '~/components/HouseKeepingComponents/ui/Flex';

type Props = {
  options: Array<{
    value: string | number;
    label: string;
    description?: string;
    icon?: ReactNode;
    onClickPreview?: () => void;
  }>;
  name: string;
  formLabel: string;
  onSelect: (index: number) => void;
};

const RadioCardGroup = ({
  options,
  name,
  formLabel,
  onSelect,
}: Props): ReactElement => {
  const styles = useStyleSheet(themedStyles);

  return (
    <Field>
      {({ form: { values, setFieldValue } }: FormikValues) => {
        const selectedIndex = options.findIndex(
          field => field.value === values[name],
        );
        return (
          <>
            <Text style={styles.formLabel} category="p2">
              {formLabel}
            </Text>
            <RadioGroup
              selectedIndex={selectedIndex}
              onChange={(index: number) => {
                const { value } = options[index];
                if (onSelect) {
                  onSelect(index);
                }
                setFieldValue(name, value);
              }}
            >
              {options.map(
                (
                  { value, label, description, icon, onClickPreview },
                  index,
                ) => (
                  <Radio key={value} style={{ marginLeft: -20 }}>
                    {() => (
                      <Group
                        style={[
                          styles.content,
                          index === selectedIndex ? styles.selected : {},
                        ]}
                      >
                        {icon && (
                          <View style={styles.iconContainer}>{icon}</View>
                        )}
                        <Flex style={styles.textContainer}>
                          <Text category="p2" style={styles.label}>
                            {label}
                          </Text>
                          <Text
                            category="c1"
                            appearance="hint"
                            style={styles.description}
                          >
                            {description}
                          </Text>
                          {onClickPreview && (
                            <Button
                              appearance="ghost"
                              style={styles.previewButtonContainer}
                              onPress={onClickPreview}
                            >
                              Preview
                            </Button>
                          )}
                        </Flex>
                      </Group>
                    )}
                  </Radio>
                ),
              )}
            </RadioGroup>
          </>
        );
      }}
    </Field>
  );
};

const themedStyles = StyleService.create({
  formLabel: {
    fontWeight: 500,
  },
  content: {
    flex: 1,
    flexWrap: 'nowrap',
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#dee2e6',
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 8,
  },
  iconContainer: {
    width: 40,
    flexShrink: 0,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  textContainer: {
    flex: 1,
    flexDirection: 'column',
    gap: 8,
  },
  label: {
    fontWeight: '700',
    fontSize: 16,
  },
  description: {
    flex: 1,
  },
  previewButtonContainer: {
    paddingVertical: 0,
    paddingHorizontal: 0,
    marginHorizontal: -10,
    minWidth: 0,
    minHeight: 0,
  },
  selected: {
    borderColor: 'rgb(242, 76, 89)',
  },
});

export default RadioCardGroup;

import React, { useEffect, useState } from 'react';
import * as Clipboard from 'expo-clipboard';
import {
  Button,
  Text,
  StyleService,
  useStyleSheet,
} from '@ui-kitten/components';
import { CheckIcon, ClipboardIcon } from '~components/Icon';
import theme from '~/theme.json';

export type CopyButtonProps = {
  copyText?: string;
  copiedText?: string;
  timeout?: number;
  href?: string;
  shareIcons?: boolean;
  onClickShare?: () => void;
  status?: 'info' | 'primary';
  disabled?: boolean;
};

const CopyButton = ({
  copyText = 'Copy link',
  copiedText = 'Copied',
  href,
  timeout = 1000,
  onClickShare,
  shareIcons = true,
  status = 'info',
  disabled,
}: CopyButtonProps) => {
  const styles = useStyleSheet(themedStyles);
  const [copied, setCopied] = useState(false);

  useEffect(() => {
    if (copied) {
      setTimeout(() => setCopied(false), timeout);
    }
  }, [copied, timeout]);

  const copyToClipboard = () => {
    setCopied(true);
    if (onClickShare) {
      onClickShare();
    }
    if (href) {
      Clipboard.setStringAsync(href);
    }
  };

  const backgroundStyle = copied && styles.copied;
  const icon = copied ? CheckIcon : ClipboardIcon;
  const shareIconst = shareIcons ? icon : undefined;

  return (
    <Button
      appearance="filled"
      size="small"
      style={[
        !disabled && {
          backgroundColor: statusColors[status],
          borderColor: statusColors[status],
        },
        backgroundStyle,
        styles.button,
      ]}
      disabled={disabled}
      onPress={copyToClipboard}
      accessoryLeft={shareIconst}
    >
      <Text>{copied ? copiedText : copyText}</Text>
    </Button>
  );
};

export default CopyButton;

const themedStyles = StyleService.create({
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 0,
    minHeight: 0,
    height: '100%',
  },
  copied: {
    backgroundColor: 'color-success-600',
    borderColor: 'color-success-600',
  },
});

const statusColors = {
  primary: theme['color-primary-500'],
  info: theme['color-info-500'],
};

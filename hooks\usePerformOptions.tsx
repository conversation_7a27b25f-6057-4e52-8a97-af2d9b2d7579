import React from 'react';
import { View } from 'react-native';
import { HKPerformOption } from '~/types';
import { Icon } from '~node_modules/@ui-kitten/components';

type Props = {
  performPersonally: boolean;
};

const usePerformOptions = ({ performPersonally }: Props): HKPerformOption[] => [
  {
    checklist: 'person1',
    label: 'Person 1',
    description: performPersonally
      ? "I'm going to perform the tasks for Person 1"
      : 'The lead housekeeper is going to perform the tasks for Person 1',
    icon: (
      <View style={{ flexDirection: 'row', alignItems: 'center', gap: 0 }}>
        <Icon fill="black" width={24} name="person" />
        <Icon fill="black" width={16} name="person-outline" />
      </View>
    ),
  },
  {
    checklist: 'person2',
    label: 'Person 2',
    description: performPersonally
      ? "I'm going to perform the tasks for Person 2"
      : 'The lead housekeeper is going to perform the tasks for Person 2',
    icon: (
      <View style={{ flexDirection: 'row', alignItems: 'center', gap: 0 }}>
        <Icon fill="black" width={24} name="person-outline" />
        <Icon fill="black" width={16} name="person" />
      </View>
    ),
  },
  {
    checklist: 'all',
    label: 'All',
    description: performPersonally
      ? "I'm going to perform all tasks by myself"
      : 'The lead housekeeper is going to perform all tasks alone',
    icon: <Icon fill="black" width={24} name="person" />,
  },
];

export default usePerformOptions;

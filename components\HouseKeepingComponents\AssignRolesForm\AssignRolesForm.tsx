import React, { useState } from 'react';
import * as Yup from 'yup';
import { StyleService, useStyleSheet } from '@ui-kitten/components';
import { View } from 'react-native';
import { Formik } from 'formik';
import {
  PeopleOutlineIcon,
  PersonIcon,
  RightArrowIcon,
} from '~/components/Icon';
import { HKJob, HKChecklist, HKSection, HKTask } from '~/types';
import usePerformOptions from '~/hooks/usePerformOptions';
import ChecklistSections from '~/components/HouseKeepingComponents/share/ChecklistSections';
import SpinnerButton from '~components/SpinnerButton';
import RadioCardGroup from '~components/HouseKeepingComponents/share/RadioCardGroup';
import Modal from '~components/HouseKeepingComponents/ui/Modal';
import AssignRoleInput from '~components/HouseKeepingComponents/AssignRolesForm/AssignRoleInput';
import KeyboardAvoidingView from '~components/KeyboardAvoidingView';
import { formatPreviewName } from '~hk-helpers';

const schema = Yup.object().shape({
  performType: Yup.string().required('This field is required'),
  leaderPerforms: Yup.string().required('This field is required'),
  leaderName: Yup.string().required('This field is required'),
  helperName: Yup.string().when('leaderPerforms', {
    is: 'all',
    then: Yup.string(),
    otherwise: Yup.string().required('Required'),
  }),
});

const checklistOptions = [
  {
    checklist: 'personally',
    label: 'Yes',
    description: 'I am going to perform the checklist fully or partially.',
    icon: <PersonIcon fill="black" width={24} />,
  },
  {
    checklist: 'assigned',
    label: 'No',
    description: 'I am going to assign the whole checklist to other people.',
    icon: <PeopleOutlineIcon fill="black" width={24} />,
  },
];

type Props = {
  onSave: (values: HKJob) => Promise<void>;
  values?: HKJob | null;
  sections?: HKSection[];
  tasks?: HKTask[];
};

const AssignRolesForm = ({ values, sections, tasks, onSave }: Props) => {
  const styles = useStyleSheet(themedStyle);
  const [performPersonally, setPerformPersonally] = useState<boolean>(
    values ? values.performType === 'personally' : true,
  );
  const [perform, setPerform] = useState<HKChecklist | null>(
    values?.leaderPerforms ?? null,
  );
  const [submitting, setSubmitting] = useState(false);
  const [checklistPreview, setChecklistPreview] = useState<HKChecklist | null>(
    null,
  );
  const performOptions = usePerformOptions({ performPersonally });

  const onSubmit = async (submitValues: HKJob) => {
    setSubmitting(true);
    await onSave(submitValues);
    setSubmitting(false);
  };

  const handleClickPreview = (checklist: HKChecklist) => {
    setChecklistPreview(checklist);
  };
  const handleCloseChecklistPreview = () => {
    setChecklistPreview(null);
  };

  const showPreview = tasks && sections;

  const spinnerText = values || !performPersonally ? 'Save' : 'Start';

  return (
    <>
      <KeyboardAvoidingView>
        <Formik
          initialValues={
            values ?? {
              jobId: null,
              performType: 'personally',
              leaderPerforms: 'person1',
              leaderName: '',
              helperName: '',
            }
          }
          onSubmit={onSubmit}
          validationSchema={schema}
          enableReinitialize
          validateOnMount
        >
          {({ values: formValues, setFieldValue, handleSubmit, isValid }) => (
            <View style={styles.container}>
              <RadioCardGroup
                name="performType"
                formLabel="Will you be the one performing the checklist personally?"
                options={checklistOptions.map(
                  ({ checklist, ...performOption }) => ({
                    ...performOption,
                    value: checklist,
                  }),
                )}
                onSelect={index => {
                  setPerformPersonally(index === 0);
                  setFieldValue(
                    'performType',
                    index === 0 ? 'personally' : 'assigned',
                  );
                }}
              />
              <RadioCardGroup
                name="leaderPerforms"
                formLabel={
                  performPersonally
                    ? 'Which checklist are you going to follow?'
                    : 'Which checklist is the lead housekeeper going to follow?'
                }
                options={performOptions.map(
                  ({ checklist, ...performOption }) => ({
                    ...performOption,
                    value: checklist,
                    onClickPreview:
                      showPreview && (() => handleClickPreview(checklist)),
                  }),
                )}
                onSelect={index => {
                  setPerform(performOptions[index].checklist);
                  setFieldValue(
                    'leaderPerforms',
                    performOptions[index].checklist,
                  );
                }}
              />
              <AssignRoleInput
                label={
                  performPersonally
                    ? "What's your name?"
                    : "What's the lead housekeeper's name?"
                }
                value={formValues.leaderName}
                onChangeText={value => setFieldValue('leaderName', value)}
              />
              {perform !== 'all' && (
                <AssignRoleInput
                  label="What's the name of the other person?"
                  value={formValues.helperName}
                  onChangeText={value => setFieldValue('helperName', value)}
                />
              )}
              <SpinnerButton
                text={!submitting ? spinnerText : ''}
                accessoryRight={!submitting ? RightArrowIcon : undefined}
                onPress={() => handleSubmit()}
                isLoading={submitting}
                disabled={!isValid}
              />
            </View>
          )}
        </Formik>
      </KeyboardAvoidingView>
      {showPreview && !!checklistPreview && (
        <Modal
          visible={!!checklistPreview}
          onClose={handleCloseChecklistPreview}
          title={`Preview ${formatPreviewName(checklistPreview)}`}
          stickyHeader
        >
          <ChecklistSections
            sections={sections}
            checklist={checklistPreview}
            tasks={tasks.filter(task => task[checklistPreview])}
          />
        </Modal>
      )}
    </>
  );
};

export default AssignRolesForm;

const themedStyle = StyleService.create({
  container: {
    gap: 16,
    marginVertical: 16,
  },
  modalHeader: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    padding: 16,
    backgroundColor: 'white',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    zIndex: 1,
  },
});

import React from 'react';
import {
  Icon,
  IconElement,
  IconProps,
} from '~node_modules/@ui-kitten/components';

export const SkillsetIcon = (props: IconProps): IconElement => (
  <Icon {...props} name="color-palette-outline" />
);

export const ClockIcon = (props: IconProps): IconElement => (
  <Icon {...props} name="clock" />
);

export const ClockOutlineIcon = (props: IconProps): IconElement => (
  <Icon {...props} name="clock-outline" />
);

export const HomeIcon = (props: IconProps): IconElement => (
  <Icon {...props} name="home-outline" />
);

export const RecurringIcon = (props: IconProps): IconElement => (
  <Icon {...props} name="repeat-outline" />
);

export const PinIcon = (props: IconProps): IconElement => (
  <Icon {...props} name="pin-outline" />
);

export const PersonIcon = (style: IconProps): IconElement => (
  <Icon {...style} name="person-outline" />
);
export const EmailIcon = (style: IconProps): IconElement => (
  <Icon {...style} name="email" />
);

export const PlusIcon = (style: IconProps): IconElement => (
  <Icon {...style} name="plus" />
);

export const PeopleIcon = (style: IconProps): IconElement => (
  <Icon {...style} name="people" />
);

export const PeopleOutlineIcon = (style: IconProps): IconElement => (
  <Icon {...style} name="people-outline" />
);

export const PersonAddOutlineIcon = (style: IconProps): IconElement => (
  <Icon {...style} name="person-add-outline" />
);

export const PersonDoneOutlineIcon = (style: IconProps): IconElement => (
  <Icon {...style} name="person-done-outline" />
);

export const KeyIcon = (style: IconProps): IconElement => (
  <Icon {...style} name="keypad" />
);

export const LogOutIcon = (style: IconProps): IconElement => (
  <Icon {...style} name="log-out" />
);

export const MoneyIcon = (style: IconProps): IconElement => (
  <Icon {...style} name="credit-card" />
);

export const CloseIcon = (style: IconProps): IconElement => (
  <Icon {...style} name="close" />
);

export const EditIcon = (style: IconProps): IconElement => (
  <Icon {...style} name="edit" />
);

export const TrashIcon = (style: IconProps): IconElement => (
  <Icon {...style} name="trash-outline" />
);

export const ListOutlineIcon = (style: IconProps): IconElement => (
  <Icon {...style} name="list" />
);

export const LeftArrowIcon = (style: IconProps): IconElement => (
  <Icon {...style} name="arrow-back-outline" />
);

export const RightArrowIcon = (style: IconProps): IconElement => (
  <Icon {...style} name="arrow-forward-outline" />
);

export const DeleteAccountIcon = (style: IconProps): IconElement => (
  <Icon {...style} name="person-delete-outline" />
);

export const InfoIcon = (style: IconProps): IconElement => (
  <Icon {...style} name="info-outline" />
);

export const HelpIcon = (style: IconProps): IconElement => (
  <Icon {...style} name="question-mark-circle-outline" />
);

export const CalendarIcon = (style: IconProps): IconElement => (
  <Icon {...style} name="calendar-outline" />
);

export const ActivitiesIcon = (style: IconProps): IconElement => (
  <Icon {...style} name="activity-outline" />
);

export const CameraIcon = (style: IconProps): IconElement => (
  <Icon {...style} name="camera-outline" />
);

export const VideoIcon = (style: IconProps): IconElement => (
  <Icon {...style} name="video-outline" />
);

export const PhotoIcon = (style: IconProps): IconElement => (
  <Icon {...style} name="image-outline" />
);

export const FolderIcon = (style: IconProps): IconElement => (
  <Icon {...style} name="folder-outline" />
);

export const CheckIcon = (style: IconProps): IconElement => (
  <Icon {...style} name="checkmark-outline" />
);

export const ClipboardIcon = (style: IconProps): IconElement => (
  <Icon {...style} name="clipboard-outline" />
);

export const ShareIcon = (style: IconProps): IconElement => (
  <Icon {...style} name="share-outline" />
);

export const FileTextIcon = (style: IconProps): IconElement => (
  <Icon {...style} name="file-text-outline" />
);

export const CarIcon = (style: IconProps): IconElement => (
  <Icon {...style} name="car-outline" />
);

export const UnlockIcon = (style: IconProps): IconElement => (
  <Icon {...style} name="unlock-outline" />
);

export default (props: IconProps): IconElement => <Icon {...props} />;

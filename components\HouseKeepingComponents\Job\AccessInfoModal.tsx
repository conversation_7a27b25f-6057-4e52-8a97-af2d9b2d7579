/* eslint-disable camelcase */
import React from 'react';
import { View } from 'react-native';
import {
  StyleService,
  useStyleSheet,
  Text,
  Divider,
} from '@ui-kitten/components';
import Drawer from '~components/HouseKeepingComponents/ui/Drawer';
import { HKExtendedJob } from '~/types';
import { UnlockIcon, FileTextIcon, CarIcon } from '~/components/Icon';
import Flex from '~/components/HouseKeepingComponents/ui/Flex';

type Props = {
  job: HKExtendedJob | null;
  opened: boolean;
  onClose: () => void;
};

const AccessInfoModal = ({ job, opened, onClose }: Props) => {
  const styles = useStyleSheet(themedStyles);

  if (!job) {
    return null;
  }

  const {
    property: {
      propertyAddress,
      vitals: {
        access_elevator,
        access_garage,
        access_gate,
        access_parking_location,
        access_notes,
        access_parking_spots,
        access_howto,
        access_wifi_password,
        access_wifi_name,
        access_lockbox_location,
        access_lockbox_code,
        access_intercom,
      },
    },
  } = job;

  const accessInfo = [
    {
      icon: <UnlockIcon fill="black" width={24} height={24} />,
      title: 'How to get into the Property',
      items: [
        {
          title: 'Lockbox or Smartlock Location',
          content: access_lockbox_location,
        },
        {
          title: 'Lockbox or Smartlock Code',
          content: access_lockbox_code,
        },
        {
          title: 'Property Address',
          content: propertyAddress,
        },
        {
          title: 'Intercom Number',
          content: access_intercom,
        },
        {
          title: 'Gate Access Code',
          content: access_gate,
        },
        {
          title: 'Elevator Access Code',
          content: access_elevator,
        },
        {
          title: 'Garage Access Code',
          content: access_garage,
        },
        {
          title: 'How to get in',
          content: access_howto,
        },
      ],
    },
    {
      icon: <CarIcon fill="black" width={24} height={24} />,
      title: 'Where to park your car',
      items: [
        {
          title: 'Parking Location',
          content: access_parking_location,
        },
        {
          title: 'Parking Spots',
          content: access_parking_spots,
        },
      ],
    },
    {
      icon: <FileTextIcon fill="black" width={24} height={24} />,
      title: 'Other things to note',
      items: [
        {
          title: 'Notes',
          content: access_notes,
        },
        {
          title: 'WiFi Name',
          content: access_wifi_name,
        },
        {
          title: 'WiFi Password',
          content: access_wifi_password,
        },
      ],
    },
  ];

  return (
    <Drawer
      visible={opened}
      onClose={onClose}
      title="Property Access Information"
    >
      {accessInfo.map(category => (
        <View key={category.title} style={styles.section}>
          <View style={styles.sectionHeader}>
            {category.icon}
            <Text style={styles.sectionTitle}>{category.title}</Text>
          </View>
          <Divider />
          {category.items.map(({ title, content }) => {
            if (!content) {
              return null;
            }
            return (
              <>
                <Flex key={title} style={styles.itemRow}>
                  <View style={styles.itemLabelContainer}>
                    <Text style={styles.itemLabel}>{title}</Text>
                  </View>
                  <View style={styles.itemValueContainer}>
                    <Text style={styles.itemValue}>{content}</Text>
                  </View>
                </Flex>
                <Divider />
              </>
            );
          })}
        </View>
      ))}
    </Drawer>
  );
};

export default AccessInfoModal;

const themedStyles = StyleService.create({
  section: {
    marginBottom: 24,
  },
  sectionHeader: {
    gap: 10,
    flexDirection: 'row',
    alignItems: 'center',
    paddingBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
  },
  itemRow: {
    paddingVertical: 12,
  },
  itemLabel: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  itemValue: {
    fontSize: 14,
    color: '#212529',
  },
  itemLabelContainer: {
    flex: 2,
  },
  itemValueContainer: {
    flex: 3,
  },
});

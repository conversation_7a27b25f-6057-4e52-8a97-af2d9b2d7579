import React, {
  useCallback,
  useEffect,
  useLayoutEffect,
  useState,
} from 'react';
import { Formik } from 'formik';
import {
  Divider,
  Layout,
  useStyleSheet,
  StyleService,
  Input,
  Icon,
  OverflowMenu,
  MenuItem,
} from '@ui-kitten/components';
import { StackScreenProps } from '@react-navigation/stack';
import { Image, Text, TouchableOpacity, View } from 'react-native';
import { useMutation, useQuery, useQueryClient } from 'react-query';
import * as Yup from 'yup';
import { useToast } from 'react-native-toast-notifications';
import MediaPicker, { MediaPickerSources } from '~components/MediaPicker';
import KeyboardAvoidingView from '~components/KeyboardAvoidingView';
import * as ExpoImagePicker from '~node_modules/expo-image-picker';
import Confirm from '~components/Confirm';
import useTasks from '~api/useTasks';
import SpinnerButton from '~components/SpinnerButton';
import useTaskAssignment from '~hooks/useTaskAssignment';
import WorktimeInputField from '~components/Inputs/WorktimeInput/WorktimeInputField';
import useUser from '~api/useUser';
import RecurringTaskOverdueWarning from '~components/TaskCardComponents/RecurringTaskOverdueWarning';
import Spinner from '~components/Spinner';

const schema = Yup.object().shape({
  video: Yup.mixed().required('Required'),
  time: Yup.number().positive().required('Required'),
  hourlyRate: Yup.string().required('Required'),
  costOfLabor: Yup.string().required('Required'),
  costOfMaterials: Yup.number().nullable(),
  receipts: Yup.array().when('costOfMaterials', {
    is: value => value !== undefined && value > 0,
    then: Yup.array().min(1),
  }),
});

type Props = StackScreenProps;

const FinishTask = ({ navigation, route }: Props) => {
  const toast = useToast();
  const { taskAssignmentId } = route.params;
  const { data: taskAssignment, isLoading: taskAssignmentIsLoading } =
    useTaskAssignment({
      id: taskAssignmentId,
    });
  const [overdueInfo, setOverdueInfo] = useState<{
    daysLate: number;
    nextRecurringPreferredDate: string;
    diffInDaysToNextRecurringPreferredDate: number;
  } | null>(null);
  const { getUser } = useUser();

  const {
    data: { hourlyRate: lockedHourlyRate },
    isLoading: hourlyRateIsLoading,
  } = useQuery('user', () => getUser(taskAssignment?.task.accountId));

  useLayoutEffect(() => {
    navigation.setOptions({
      headerTitle: taskAssignment?.finishedAt
        ? 'Edit Job Completion'
        : 'Finish Job',
    });
  }, [navigation, taskAssignment?.finishedAt]);

  const { finishTaskAssignment } = useTasks();
  const queryClient = useQueryClient();
  const { mutate: finishTaskAssignmentMutate, isLoading: finishTaskIsLoading } =
    useMutation(finishTaskAssignment, {
      onSuccess: () => {
        queryClient.invalidateQueries('task-assignments');
        queryClient.invalidateQueries(['completed-tasks']);
        queryClient.invalidateQueries('tasks');
        queryClient.invalidateQueries(['task-assignments', taskAssignmentId]);
      },
    });

  if (taskAssignmentIsLoading || hourlyRateIsLoading) {
    return <Spinner />;
  }

  if (!taskAssignment) {
    return null;
  }

  const doNavigationAfterFinishTask = () => {
    if (taskAssignment?.finishedAt) {
      navigation.goBack();
    } else {
      navigation.navigate('MyTasksScreen');
    }
  };

  const handleConfirmOverdue = () => {
    setOverdueInfo(null);
    doNavigationAfterFinishTask();
  };

  const handleFinishTask = values => {
    const data = {
      id: taskAssignmentId,
      finishedAt: new Date().toISOString(),
      price: values.price,
      video: values.video,
      thumbnail: values.thumbnail,
      costOfMaterials: values.costOfMaterials
        ? parseFloat(values.costOfMaterials)
        : 0,
      costOfLabor: values.costOfLabor ? parseFloat(values.costOfLabor) : 0,
      timeSpent: values.time,
      receipts: values.receipts,
      hourlyRate: values.hourlyRate,
      comments: values.comments,
    };

    finishTaskAssignmentMutate(data, {
      onSuccess: response => {
        toast.show(
          taskAssignment?.finishedAt
            ? 'This job has been saved.'
            : 'This job has been moved to My Invoices and is no longer available in My Jobs.',
        );

        if (response.showOverdueWarning) {
          setOverdueInfo({
            daysLate: response.details.daysLate,
            nextRecurringPreferredDate:
              response.details.nextRecurringPreferredDate,
            diffInDaysToNextRecurringPreferredDate:
              response.details.diffInDaysToNextRecurringPreferredDate,
          });
        } else {
          doNavigationAfterFinishTask();
        }
      },
    });
  };

  const initialValues = {
    costOfMaterials: taskAssignment?.costOfMaterials,
    costOfLabor: taskAssignment?.costOfLabor,
    time: taskAssignment?.timeSpent,
    hourlyRate: taskAssignment?.hourlyRate,
    video: taskAssignment?.video,
    thumbnail: taskAssignment?.thumbnail,
    receipts: taskAssignment?.receipts.map(({ mediaUrl }) => ({
      uri: mediaUrl,
    })),
    comments: taskAssignment?.comments,
  };

  return (
    <>
      <Formik
        initialValues={initialValues}
        onSubmit={handleFinishTask}
        validationSchema={schema}
        validateOnMount
      >
        {({
          handleChange,
          handleBlur,
          handleSubmit,
          values,
          setFieldValue,
          isValid,
        }) => (
          <FinishTaskForm
            values={values}
            setFieldValue={setFieldValue}
            handleChange={handleChange}
            handleBlur={handleBlur}
            handleSubmit={handleSubmit}
            isValid={isValid}
            taskAssignment={taskAssignment}
            lockedHourlyRate={lockedHourlyRate}
            finishTaskIsLoading={finishTaskIsLoading}
          />
        )}
      </Formik>
      {overdueInfo && (
        <Confirm
          confirmTitle="Warning"
          show
          onConfirm={handleConfirmOverdue}
          text={
            <RecurringTaskOverdueWarning
              daysLate={overdueInfo.daysLate}
              nextRecurringPreferredDate={
                overdueInfo.nextRecurringPreferredDate
              }
              diffInDaysToNextRecurringPreferredDate={
                overdueInfo.diffInDaysToNextRecurringPreferredDate
              }
            />
          }
          confirmButtonText="OK"
        />
      )}
    </>
  );
};

const FinishTaskForm = ({
  values,
  setFieldValue,
  handleChange,
  handleBlur,
  handleSubmit,
  isValid,
  taskAssignment,
  lockedHourlyRate,
  finishTaskIsLoading,
}) => {
  const styles = useStyleSheet(themedStyles);
  const [deleteReceipt, setDeleteReceipt] = useState<number | null>(null);
  const [visible, setVisible] = React.useState(false);

  const renderToggleButton = () => (
    <TouchableOpacity onPress={() => setVisible(true)}>
      <Icon fill="white" style={styles.plusIcon} name="plus" />
    </TouchableOpacity>
  );

  const receiptUploadButtonsToRender = [
    { title: 'Camera', sourceType: MediaPickerSources.Camera },
    {
      title: 'Gallery',
      sourceType: MediaPickerSources.Gallery,
    },
    {
      title: 'Documents',
      sourceType: MediaPickerSources.Document,
    },
  ];

  const trigger = handleMediaPicked => {
    const handleSelect = index => {
      handleMediaPicked(
        receiptUploadButtonsToRender[index.row].sourceType,
        ExpoImagePicker.MediaTypeOptions.Images,
      );
    };
    return (
      <View style={styles.receipt}>
        <OverflowMenu
          anchor={renderToggleButton}
          visible={visible}
          onSelect={handleSelect}
          onBackdropPress={() => setVisible(false)}
        >
          {receiptUploadButtonsToRender.map(item => (
            <MenuItem key={item.title} title={item.title} />
          ))}
        </OverflowMenu>
      </View>
    );
  };

  const handleChangeTime = useCallback(
    (value: number) => {
      const numericTime = value;
      const numericHourlyRate = parseFloat(values.hourlyRate);

      if (numericHourlyRate) {
        const costOfLabor = numericTime * numericHourlyRate;
        setFieldValue(
          'costOfLabor',
          costOfLabor ? costOfLabor.toFixed(2).toString() : '',
        );
      }
      setFieldValue('time', value);
    },
    [setFieldValue, values.hourlyRate],
  );

  useEffect(() => {
    if (lockedHourlyRate && !values.hourlyRate) {
      setFieldValue('hourlyRate', lockedHourlyRate);
    }
  }, [lockedHourlyRate, setFieldValue, values.hourlyRate]);

  useEffect(() => {
    handleChangeTime(values.time);
  }, [handleChangeTime, values.time]);

  useEffect(() => {
    setVisible(false);
  }, [values.receipts]);

  const handleChangeCostOfLabor = value => {
    const numericCostOfLabor = parseFloat(value);
    const numericTime = parseFloat(values.time);

    if (numericTime) {
      const hourlyRate = numericCostOfLabor / numericTime;
      setFieldValue(
        'hourlyRate',
        hourlyRate ? hourlyRate.toFixed(2).toString() : '',
      );
    }
    setFieldValue('costOfLabor', value);
  };

  const handleChangeHourlyRate = value => {
    const numericHourlyRate = parseFloat(value);
    const numericTime = parseFloat(values.time);
    if (numericTime) {
      const costOfLabor = numericTime * numericHourlyRate;

      setFieldValue(
        'costOfLabor',
        costOfLabor ? costOfLabor.toFixed(2).toString() : '',
      );
    }
    setFieldValue('hourlyRate', value);
  };

  const removeReceipt = index => {
    const receipts = [...values.receipts];
    receipts.splice(index, 1);
    setFieldValue('receipts', receipts);
    setDeleteReceipt(null);
  };

  return (
    <KeyboardAvoidingView style={styles.container}>
      <Layout style={styles.form} level="1">
        <MediaPicker
          onChange={({ media, thumbnail }) => {
            if (media && !media.canceled) {
              setFieldValue('video', media);
            }
            if (thumbnail) {
              setFieldValue('thumbnail', thumbnail);
            }
          }}
          label="Record a Video or Take a Photo evidence of the completed job"
          description="Remember to speak clearly during the recording"
          mediaTypes={ExpoImagePicker.MediaTypeOptions.All}
          thumbnail={taskAssignment?.thumbnail}
          defaultValue={taskAssignment?.video}
        />
        <View style={styles.sideBySideInputRow}>
          <WorktimeInputField
            style={[styles.input, styles.sideBySideInput]}
            name="time"
          />
          <Input
            style={[styles.input, styles.sideBySideInput]}
            label="Hourly rate (USD)"
            placeholder="e.g. 35.5"
            value={values.hourlyRate}
            onChangeText={handleChangeHourlyRate}
            keyboardType="decimal-pad"
            disabled={Boolean(lockedHourlyRate)}
          />
          <Input
            style={[styles.input, styles.sideBySideInput]}
            label="Cost of labor (USD)"
            placeholder="e.g. 120.34"
            value={values.costOfLabor}
            onChangeText={handleChangeCostOfLabor}
            keyboardType="decimal-pad"
          />
        </View>
        <Input
          style={styles.input}
          label="Cost of materials (USD)"
          placeholder="e.g. 120.34"
          value={values.costOfMaterials}
          onChangeText={handleChange('costOfMaterials')}
          onBlur={handleBlur('costOfMaterials')}
          keyboardType="decimal-pad"
        />
        {values.costOfMaterials > 0 && (
          <>
            <Text style={styles.receiptsText}>Add Receipts for this Job</Text>
            <View style={styles.receipts}>
              {values.receipts.map(({ uri, mimeType }, index) => (
                <TouchableOpacity
                  key={uri}
                  onPress={() => setDeleteReceipt(index)}
                >
                  {mimeType === 'application/pdf' ? (
                    <View style={styles.receipt}>
                      <Text>PDF</Text>
                    </View>
                  ) : (
                    <Image
                      style={styles.receipt}
                      source={{
                        uri,
                      }}
                    />
                  )}
                </TouchableOpacity>
              ))}
              <MediaPicker
                onChange={({ media }) => {
                  setFieldValue('receipts', [...values.receipts, media]);
                }}
                trigger={trigger}
                mediaTypes={ExpoImagePicker.MediaTypeOptions.Images}
                onCancel={() => setVisible(false)}
              />
            </View>
          </>
        )}
        <Input
          label="Comments"
          style={styles.input}
          textStyle={styles.multilineInput}
          multiline
          numberOfLines={4}
          textAlignVertical="top"
          placeholder="Enter your commets about the Task (optional)"
          value={values.comments}
          onChangeText={e => setFieldValue('comments', e)}
        />
      </Layout>
      <Divider />
      <SpinnerButton
        text={taskAssignment?.finishedAt ? 'Save' : 'Finish Job'}
        style={styles.addButton}
        onPress={handleSubmit}
        isLoading={finishTaskIsLoading}
        disabled={!isValid}
      />
      <Confirm
        show={deleteReceipt !== null}
        onCancel={() => setDeleteReceipt(null)}
        onConfirm={() => removeReceipt(deleteReceipt)}
        text="Do you want to delete this Receipt?"
      />
    </KeyboardAvoidingView>
  );
};

const themedStyles = StyleService.create({
  container: {
    flex: 1,
    backgroundColor: 'background-basic-color-2',
  },
  form: {
    flex: 1,
    paddingHorizontal: 4,
    paddingVertical: 24,
  },
  input: {
    marginHorizontal: 12,
    marginVertical: 8,
  },
  sideBySideInputRow: {
    flexDirection: 'row',
    marginHorizontal: 12,
    gap: 12,
  },
  sideBySideInput: {
    flex: 1,
    alignSelf: 'flex-end',
    marginHorizontal: 0,
  },
  multilineInput: {
    height: 120,
    marginHorizontal: 12,
    marginVertical: 8,
  },
  middleInput: {
    width: 128,
  },
  addButton: {
    marginHorizontal: 16,
    marginVertical: 24,
  },
  receipts: {
    display: 'flex',
    flexDirection: 'row',
    marginLeft: 12,
    marginTop: 12,
  },
  receipt: {
    backgroundColor: 'gray',
    width: 60,
    height: 60,
    borderRadius: 5,
    marginRight: 12,
    marginBottom: 12,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
  backdrop: {
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  plusIcon: {
    width: 40,
    height: 40,
  },
  receiptsText: {
    fontSize: 12,
    color: 'gray',
    fontWeight: '700',
    marginHorizontal: 12,
    marginTop: 12,
  },
});

export default FinishTask;

import React, { ReactElement } from 'react';
import { View } from 'react-native';

import {
  Icon,
  StyleService,
  Text,
  TopNavigationAction,
  useStyleSheet,
} from '@ui-kitten/components';

type HeaderButtonProps = {
  buttonText?: string;
  style?: any;
  iconName: string;
  iconPack?: string;
};

type HeaderButtonActionProps = {
  onPress: () => void;
  buttonText?: string;
  iconName: string;
  iconPack?: string;
};

const HeaderButton = ({
  buttonText,
  style,
  iconName,
  iconPack = 'eva',
}: HeaderButtonProps) => {
  const styles = useStyleSheet(themedStyle);

  return (
    <View style={styles.addButton}>
      <Icon style={style} name={iconName} pack={iconPack} />
      <Text style={styles.addButtonText}>{buttonText}</Text>
    </View>
  );
};

const HeaderButtonAction = ({
  onPress,
  buttonText,
  iconName,
  iconPack,
}: HeaderButtonActionProps): ReactElement => (
  <TopNavigationAction
    icon={
      <HeaderButton
        iconName={iconName}
        iconPack={iconPack}
        buttonText={buttonText}
      />
    }
    onPress={onPress}
  />
);

const themedStyle = StyleService.create({
  addButtonText: { marginLeft: 10 },
  addButton: { flexDirection: 'row', alignItems: 'center' },
});

export default HeaderButtonAction;

import React, { ReactNode, useEffect, useRef } from 'react';
import {
  View,
  Animated,
  Dimensions,
  PanResponder,
  Modal,
  ScrollView,
} from 'react-native';
import { StyleService, useStyleSheet } from '@ui-kitten/components';
import ModalHeader from '~components/HouseKeepingComponents/ui/ModalHeader';

type Props = {
  visible: boolean;
  onClose: () => void;
  title: string;
  children: ReactNode;
};

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('screen');

const Drawer = ({ visible, onClose, title, children }: Props) => {
  const styles = useStyleSheet(themedStyles);
  const slideAnim = useRef(new Animated.Value(-SCREEN_WIDTH)).current;

  const handleClose = () => {
    Animated.timing(slideAnim, {
      toValue: -SCREEN_WIDTH,
      duration: 100,
      useNativeDriver: true,
    }).start(() => {
      onClose();
    });
  };

  useEffect(() => {
    if (visible) {
      slideAnim.setValue(-SCREEN_WIDTH);
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 250,
        useNativeDriver: true,
      }).start();
    }
  }, [slideAnim, visible]);

  const panResponder = useRef(
    PanResponder.create({
      onMoveShouldSetPanResponder: (_evt, gestureState) =>
        gestureState.dx < -10 && Math.abs(gestureState.dy) < 50,
      onPanResponderMove: (_evt, gestureState) => {
        if (gestureState.dx < 0) {
          slideAnim.setValue(gestureState.dx);
        }
      },
      onPanResponderRelease: (_evt, gestureState) => {
        if (gestureState.dx < -SCREEN_WIDTH * 0.3) {
          handleClose();
        } else {
          Animated.timing(slideAnim, {
            toValue: 0,
            duration: 200,
            useNativeDriver: true,
          }).start();
        }
      },
    }),
  ).current;

  if (!visible) {
    return null;
  }

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      onRequestClose={handleClose}
      statusBarTranslucent
    >
      <View style={styles.container}>
        <Animated.View
          style={[
            styles.drawer,
            {
              transform: [{ translateX: slideAnim }],
            },
          ]}
          {...panResponder.panHandlers}
        >
          <ModalHeader title={title} onClose={handleClose} stickyHeader />
          <ScrollView style={styles.content}>{children}</ScrollView>
        </Animated.View>
      </View>
    </Modal>
  );
};

const themedStyles = StyleService.create({
  container: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  drawer: {
    width: SCREEN_WIDTH,
    height: SCREEN_HEIGHT,
    backgroundColor: 'white',
  },
  header: {
    justifyContent: 'space-between',
    padding: 16,
    paddingRight: 8,
  },
  closeButton: {
    width: 36,
    height: 36,
    paddingVertical: 0,
    paddingHorizontal: 0,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
});

export default Drawer;

import React from 'react';
import { View } from 'react-native';

import { ButtonProps } from '@ui-kitten/components';
import { Button, Spinner } from '~node_modules/@ui-kitten/components';

const LoadingIndicator = ({ size = 'small' }) => (
  <View style={{ marginVertical: -20 }}>
    <Spinner size={size} status="basic" />
  </View>
);

type Props = {
  text: string;
  isLoading: boolean;
  onPress: ButtonProps['onPress'];
  style?: ButtonProps['style'];
  disabled?: boolean;
  size?: ButtonProps['size'];
  accessoryRight?: ButtonProps['accessoryRight'];
};

const SpinnerButton = ({
  text,
  isLoading,
  onPress,
  style = {},
  disabled = false,
  size = 'giant',
  accessoryRight,
}: Props) => (
  <Button
    style={style}
    size={size}
    onPress={onPress}
    accessoryLeft={isLoading ? <LoadingIndicator size={size} /> : undefined}
    disabled={isLoading || disabled}
    accessoryRight={accessoryRight}
  >
    {text}
  </Button>
);

export default SpinnerButton;

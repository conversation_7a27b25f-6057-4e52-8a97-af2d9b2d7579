import React, { ComponentType } from 'react';
import {
  StyleSheet,
  ImageStyle,
  ViewStyle,
  TextStyle,
  ColorValue,
} from 'react-native';
import {
  IconElement,
  IconProps,
  StyleService,
  useStyleSheet,
} from '@ui-kitten/components';
import VectorIcon from 'react-native-vector-icons/MaterialIcons';

const MaterialVectorIcon = VectorIcon as ComponentType<{
  name: string;
  size: number;
  color?: ColorValue;
  style?: ViewStyle | TextStyle | ImageStyle;
}>;

interface MaterialIconComponentProps {
  name: string;
  style?: ViewStyle | TextStyle | ImageStyle;
  width?: number;
  height?: number;
  fill?: string;
  tintColor?: string;
}

function MaterialIconComponent({
  name,
  style,
  width,
  height,
  fill,
  tintColor,
}: MaterialIconComponentProps) {
  const themedStyle = useStyleSheet(
    StyleService.create({ style: style || {} }),
  );
  const flatStyle = StyleSheet.flatten(themedStyle.style) as ImageStyle;
  const iconSize = width || height || 24;
  const iconColor = fill || tintColor || flatStyle.tintColor;

  return (
    <MaterialVectorIcon
      name={name}
      size={iconSize}
      color={iconColor}
      style={flatStyle}
    />
  );
}

export const TimerClockIcon = (props: IconProps): IconElement => (
  <MaterialIconComponent name="timer" {...props} />
);

export const AlarmIcon = (props: IconProps): IconElement => (
  <MaterialIconComponent name="alarm" {...props} />
);
